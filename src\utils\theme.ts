// Función para obtener colores del tema
export const getThemeColors = (isDarkMode: boolean) => {
  if (isDarkMode) {
    return {
      background: '#111827', // bg-gray-900
      cardBackground: 'rgba(31, 41, 55, 0.3)', // bg-gray-800/30
      border: 'rgba(75, 85, 99, 0.3)', // border-gray-600/30
      text: '#f9fafb', // text-gray-50
      textSecondary: '#d1d5db', // text-gray-300
      accent: '#3ca2a2',
      accentHover: '#215a6d',
      secondary: 'rgba(107, 114, 128, 0.5)', // bg-gray-500/50
      secondaryHover: 'rgba(75, 85, 99, 0.5)' // bg-gray-600/50
    };
  } else {
    return {
      background: '#dfece6',
      cardBackground: '#ffffff',
      border: '#92c7a3',
      text: '#2d2d29',
      textSecondary: '#215a6d',
      accent: '#3ca2a2',
      accentHover: '#215a6d',
      secondary: '#92c7a3',
      secondaryHover: '#3ca2a2'
    };
  }
};
