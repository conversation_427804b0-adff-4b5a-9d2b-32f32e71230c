import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { socket } from '../socket';

const Login = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    // Check if user is already logged in
    const userStr = localStorage.getItem('user');
    if (userStr) {
      const user = JSON.parse(userStr);
      redirectBasedOnRole(user.role);
    }
  }, []);

  const redirectBasedOnRole = (role: string) => {
    // Mapeo de roles a rutas
    const roleRoutes: Record<string, string> = {
      'admin': '/dashboard',
      'customer_service': '/customer-service',
      'payments': '/payment-relations',
      // Puedes añadir más roles y rutas aquí sin modificar el código principal
    };

    // Obtener la ruta correspondiente al rol o usar dashboard como fallback
    const route = roleRoutes[role] || '/dashboard';

    // Redirigir al usuario
    navigate(route);

    console.log(`Usuario redirigido a ${route} basado en el rol: ${role}`);
  };

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const response = await fetch('https://aanocodb.wifigo.com.mx/api/v2/tables/msoazrhe600pa3j/records', {
        headers: {
          'Accept': 'application/json',
          'xc-token': 'S5q2DMhzneGng7KXEK7C1wlklvWrjvsCbEovxPdb'
        }
      });

      const users = await response.json();
      const user = users.list.find((u: any) =>
        u.nombre === username && u.password === password
      );

      if (user) {
        // Obtener el rol directamente de la base de datos
        const role = user.role || 'limited';

        // Guardar la información del usuario en localStorage
        localStorage.setItem('user', JSON.stringify({
          username,
          role,
          api: user.api // Guardar también el API token del usuario
        }));

        // Reconectar el socket si está desconectado
        if (!socket.connected) {
          console.log('Reconectando socket después del login...');
          socket.connect();
        }

        // Emitir evento de login al servidor
        socket.emit('user-login', {
          userId: username,
          userName: username,
          userEmail: user.email || ''
        });

        // Redirigir según el rol
        redirectBasedOnRole(role);
      } else {
        setError('Usuario o contraseña incorrectos');
      }
    } catch (error) {
      setError('Error al intentar iniciar sesión');
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8 p-8 bg-white rounded-lg shadow-lg">
        <div>
          <h2 className="text-center text-3xl font-extrabold text-gray-900">
            Iniciar Sesión
          </h2>
        </div>
        <form className="mt-8 space-y-6" onSubmit={handleLogin}>
          {error && (
            <div className="text-red-500 text-center">{error}</div>
          )}
          <div>
            <label htmlFor="username" className="sr-only">Usuario</label>
            <input
              id="username"
              name="username"
              type="text"
              required
              className="appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
              placeholder="Usuario"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
            />
          </div>
          <div>
            <label htmlFor="password" className="sr-only">Contraseña</label>
            <input
              id="password"
              name="password"
              type="password"
              required
              className="appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
              placeholder="Contraseña"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
            />
          </div>
          <div>
            <button
              type="submit"
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Iniciar Sesión
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default Login;




