import { BrowserRouter as Router, Routes, Route, Outlet } from 'react-router-dom';
import Layout from './components/Layout';
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import PaymentRelations from './pages/PaymentRelations';
import Documents from './pages/Documents';
import CustomerService from './pages/CustomerService';
import ArchivoMovimientos from './pages/ArchivoMovimientos';
import ProtectedRoute from './components/ProtectedRoute';
import { ThemeProvider } from './contexts/ThemeContext';

function App() {
  return (
    <ThemeProvider>
      <Router>
        <Routes>
          {/* Ruta de login - sin Layout */}
          <Route path="/" element={<Login />} />

          {/* Rutas protegidas - con Layout */}
          <Route element={
            <ProtectedRoute>
              <Layout>
                <Outlet />
              </Layout>
            </ProtectedRoute>
          }>
            <Route path="/dashboard" element={<Dashboard />} />
            <Route path="/payment-relations" element={<PaymentRelations />} />
            <Route path="/documents" element={<Documents />} />
            <Route path="/customer-service" element={<CustomerService />} />
            <Route path="/archivo" element={<ArchivoMovimientos />} />
          </Route>
        </Routes>
      </Router>
    </ThemeProvider>
  );
}

export default App;







