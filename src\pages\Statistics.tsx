import React from 'react';
import { Line<PERSON>hart, Line, XAxis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';

const data = [
  { name: 'Ene', pagos: 4000, clientes: 2400 },
  { name: 'Feb', pagos: 3000, clientes: 1398 },
  { name: 'Mar', pagos: 2000, clientes: 9800 },
  { name: 'Abr', pagos: 2780, clientes: 3908 },
  { name: 'May', pagos: 1890, clientes: 4800 },
  { name: 'Jun', pagos: 2390, clientes: 3800 },
];

const Statistics: React.FC = () => {
  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Estadísticas</h1>
      
      <div className="bg-white p-6 rounded-lg shadow-lg">
        <h2 className="text-xl font-semibold mb-4">Tendencias de Pagos y Clientes</h2>
        <div className="h-[400px]">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={data}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line type="monotone" dataKey="pagos" stroke="#8884d8" activeDot={{ r: 8 }} />
              <Line type="monotone" dataKey="clientes" stroke="#82ca9d" />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-lg">
          <h2 className="text-xl font-semibold mb-4">Resumen Mensual</h2>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span>Total Pagos</span>
              <span className="font-bold">$45,678</span>
            </div>
            <div className="flex justify-between items-center">
              <span>Promedio por Cliente</span>
              <span className="font-bold">$789</span>
            </div>
            <div className="flex justify-between items-center">
              <span>Clientes Nuevos</span>
              <span className="font-bold">34</span>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-lg">
          <h2 className="text-xl font-semibold mb-4">Métricas de Servicio</h2>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span>Tiempo Promedio de Respuesta</span>
              <span className="font-bold">2.5 horas</span>
            </div>
            <div className="flex justify-between items-center">
              <span>Satisfacción del Cliente</span>
              <span className="font-bold">94%</span>
            </div>
            <div className="flex justify-between items-center">
              <span>Tickets Resueltos</span>
              <span className="font-bold">156</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Statistics;