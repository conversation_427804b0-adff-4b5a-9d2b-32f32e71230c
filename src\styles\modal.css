/* Estilos para el modal */
.modal-content {
  pointer-events: auto !important; /* Asegura que el contenido del modal sea interactivo */
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2); /* Sombra para destacar el modal */
  /* border-left removido para permitir el modo oscuro desde JavaScript */
  /* background-color removido para permitir el modo oscuro desde JavaScript */
}

.modal-overlay {
  pointer-events: auto !important; /* Permite interactuar con el overlay */
}

/* Estilos para las áreas de arrastrar y soltar */
.drop-zone {
  border: 2px dashed #cbd5e0;
  padding: 1rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.drop-zone:hover, .drop-zone.drag-over {
  border-color: #4299e1;
  background-color: rgba(66, 153, 225, 0.1);
}

/* Animaciones para notificaciones */
@keyframes fadeInOut {
  0% { opacity: 0; transform: translateY(-20px); }
  10% { opacity: 1; transform: translateY(0); }
  90% { opacity: 1; transform: translateY(0); }
  100% { opacity: 0; transform: translateY(-20px); }
}

@keyframes fadeOut {
  0% { opacity: 1; }
  100% { opacity: 0; }
}

.animate-fade-in-out {
  animation: fadeInOut 3.5s ease-in-out;
}

.animate-fade-out {
  animation: fadeOut 0.5s ease-in-out forwards;
}
