import React, { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { FileText, Phone, DollarSign, Home, LogOut, Smartphone, FolderArchive, Menu, X } from 'lucide-react';
import Modal from 'react-modal';
import { api } from '../config/api';
import { socket } from '../socket';
import { QRCodeSVG } from 'qrcode.react';
import logoWifigo from '../assets/logo_wifigo.png';
import { useTheme } from '../contexts/ThemeContext';

interface LayoutProps {
  children: React.ReactNode;
}

interface ClientStatus {
  id: string;
  status: 'disconnected' | 'connecting' | 'connected' | 'error';
  qrCode: string | null;
  qrExpiresAt?: number | null;
  qrExpiresIn?: number | null;
  info: {
    number: string | null;
    name: string | null;
  } | null;
  error?: string;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const [isQRModalOpen, setIsQRModalOpen] = useState(false);
  const [isWhatsAppMenuOpen, setIsWhatsAppMenuOpen] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const [clientsStatus, setClientsStatus] = useState<{[key: string]: ClientStatus}>({});
  const [error, setError] = useState<string | null>(null);
  const [, setQrTimer] = useState<number>(0); // Estado para forzar actualizaciones del temporizador
  const [loadingQR, setLoadingQR] = useState<{[key: string]: boolean}>({});
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false); // Estado para controlar el menú móvil

  // Usar el contexto de tema
  const { isDarkMode, toggleDarkMode } = useTheme();

  // Obtener el usuario del localStorage
  const userStr = localStorage.getItem('user');
  const user = userStr ? JSON.parse(userStr) : { role: '', username: '' };
  const userRole = user.role;
  const username = localStorage.getItem('username') || user.username || '';

  // Depuración para verificar el rol del usuario
  console.log('Usuario actual:', username, 'Rol:', userRole);

  // Configurar socket.io para recibir actualizaciones en tiempo real
  useEffect(() => {
    try {
      // Escuchar eventos de actualización de estado
      socket.on('clients-status-update', (data: {[key: string]: ClientStatus}) => {
        console.log('Clients status update received:', data);
        setClientsStatus(data);
      });

      // Escuchar eventos de código QR
      socket.on('qr-code', (data: { clientId: string, qr: string, expiresAt?: number }) => {
        console.log('🔍 QR CODE RECIBIDO PARA', data.clientId, data.expiresAt ? `(expira a las ${new Date(data.expiresAt).toLocaleTimeString()})` : '');
        console.log('QR data:', data.qr ? `QR code length: ${data.qr.length}` : 'No QR code');

        // Actualizar el estado con el código QR recibido
        setClientsStatus(prev => {
          const updatedState = {
            ...prev,
            [data.clientId]: {
              ...prev[data.clientId],
              status: 'connecting' as const,
              qrCode: data.qr,
              qrExpiresAt: data.expiresAt || null
            }
          };

          console.log('Estado actualizado para', data.clientId, ':', updatedState[data.clientId].status);
          console.log('QR guardado:', updatedState[data.clientId].qrCode ? 'SÍ' : 'NO');

          // Forzar actualización de la UI
          setTimeout(() => {
            console.log('Forzando actualización de UI para mostrar QR');
            setQrTimer(prev => prev + 1);
          }, 500);

          return updatedState;
        });
      });

      // Escuchar eventos de cliente conectado
      socket.on('client-ready', (data: { clientId: string, status: string, info: any }) => {
        console.log('Client ready:', data.clientId, data);
        setClientsStatus(prev => ({
          ...prev,
          [data.clientId]: {
            ...prev[data.clientId],
            status: 'connected',
            qrCode: null,
            info: data.info
          }
        }));
      });

      // Escuchar eventos de cliente desconectado
      socket.on('client-disconnected', (data: { clientId: string }) => {
        console.log('Client disconnected:', data.clientId);
        setClientsStatus(prev => ({
          ...prev,
          [data.clientId]: {
            ...prev[data.clientId],
            status: 'disconnected',
            qrCode: null
          }
        }));
      });

      // Obtener el estado inicial de los clientes (una sola vez)
      fetchClientsStatus();

      return () => {
        // No desconectar el socket para mantener la conexión entre páginas
        // Solo limpiar los event listeners
        socket.off('clients-status-update');
        socket.off('qr-code');
        socket.off('client-ready');
        socket.off('client-disconnected');
      };
    } catch (error) {
      console.error('Error setting up socket connection:', error);
    }
  }, []);

  // Efecto para actualizar el temporizador de expiración del código QR cada segundo
  useEffect(() => {
    // Solo iniciar el temporizador si el modal está abierto y hay algún QR por mostrar
    if (isQRModalOpen && Object.values(clientsStatus).some(client => client.qrCode && client.qrExpiresAt)) {
      const timerId = setInterval(() => {
        // Verificar si algún QR ha expirado
        const currentTime = Date.now();
        let needsUpdate = false;

        Object.entries(clientsStatus).forEach(([clientId, client]) => {
          if (client.qrExpiresAt && client.qrExpiresAt <= currentTime && client.status === 'connecting') {
            // El QR ha expirado, actualizar el estado localmente
            setClientsStatus(prev => ({
              ...prev,
              [clientId]: {
                ...prev[clientId],
                status: 'disconnected',
                qrCode: null,
                qrExpiresAt: null
              }
            }));
            needsUpdate = true;
          }
        });

        // Si no hay QR expirados, solo actualizar el timer para refrescar la UI
        if (!needsUpdate) {
          setQrTimer(prev => prev + 1);
        }
      }, 1000);

      return () => clearInterval(timerId);
    }
  }, [isQRModalOpen, clientsStatus]);

  // Función para obtener el estado de los clientes (ahora usa datos locales)
  const fetchClientsStatus = async () => {
    try {
      console.log('Configurando estado de clientes con datos locales...');

      // Usar datos locales en lugar de hacer una solicitud HTTP
      const mockData = {
        default: {
          status: 'connected',
          info: {
            name: 'Sistema',
            number: 'system'
          }
        }
      };

      setClientsStatus(prevState => {
        const newState = { ...prevState };
        Object.entries(mockData).forEach(([clientId, clientData]) => {
          newState[clientId] = {
            ...newState[clientId],
            ...clientData as ClientStatus
          };
        });
        return newState;
      });

    } catch (error) {
      console.error('Error al configurar el estado de los clientes:', error);
    }
  };

  const handleLogout = () => {
    // Notificar al servidor que el usuario cerró sesión
    const userStr = localStorage.getItem('user');
    if (userStr) {
      const user = JSON.parse(userStr);
      socket.emit('user-logout', { userId: user.username || user.id });
    }

    // Desconectar el socket al cerrar sesión
    socket.disconnect();

    // Limpiar datos locales
    localStorage.removeItem('user');
    localStorage.removeItem('username');

    // Navegar al login
    navigate('/');
    setIsUserMenuOpen(false);
  };

  // Obtener la inicial del usuario
  const getUserInitial = (name: string): string => {
    return name ? name.charAt(0).toUpperCase() : 'U';
  };



  // Cerrar dropdown del usuario al hacer clic fuera
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isUserMenuOpen) {
        setIsUserMenuOpen(false);
      }
    };

    if (isUserMenuOpen) {
      document.addEventListener('click', handleClickOutside);
    }

    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [isUserMenuOpen]);

  const handleWhatsAppButtonClick = () => {
    setIsWhatsAppMenuOpen(!isWhatsAppMenuOpen);
  };

  // Función para mostrar el modal de conexión de WhatsApp (solo para administradores)
  const showWhatsAppConnectModal = () => {
    // Verificar si el usuario tiene permisos (admin o customer_service)
    if (userRole === 'admin' || userRole === 'customer_service') {
      setIsQRModalOpen(true);
      setIsWhatsAppMenuOpen(false);
    } else {
      console.warn('Intento de acceso a funcionalidad restringida por usuario no autorizado');
    }
  };

  // Función para verificar si hay algún cliente conectado
  const hasConnectedClients = () => {
    return Object.values(clientsStatus).some(client => client.status === 'connected');
  };

  // Función para verificar si un cliente específico está conectado
  const isClientConnected = (clientId: string) => {
    return clientsStatus[clientId]?.status === 'connected';
  };

  // Función para generar un código QR para un cliente específico (simulada)
  const generateQRCode = async (clientId: string) => {
    try {
      setError(null);
      console.log(`Simulando generación de QR para ${clientId}`);

      // Activar el indicador de carga
      setLoadingQR(prev => ({
        ...prev,
        [clientId]: true
      }));

      // Actualizar el estado a 'connecting' inmediatamente para dar feedback visual
      setClientsStatus(prev => ({
        ...prev,
        [clientId]: {
          ...prev[clientId],
          status: 'connecting',
          qrCode: null // Limpiar el QR anterior si existe
        }
      }));

      // Simular una demora en la generación del QR
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Generar un QR de ejemplo
      const mockQRCode = "https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=WhatsAppSimulatedQRCode";
      const expiresAt = Date.now() + 60000; // Expira en 1 minuto

      setClientsStatus(prev => ({
        ...prev,
        [clientId]: {
          ...prev[clientId],
          status: 'connecting',
          qrCode: mockQRCode,
          qrExpiresAt: expiresAt
        }
      }));

      console.log(`QR simulado generado para ${clientId}`);
    } catch (error: any) {
      console.error('Error al simular QR:', error);
      setError(`Error al generar QR para ${clientId}: ${error.message}`);
    } finally {
      // Desactivar el indicador de carga
      setLoadingQR(prev => ({
        ...prev,
        [clientId]: false
      }));
    }
  };

  // Función para desconectar un cliente (simulada)
  const disconnectClient = async (clientId: string) => {
    try {
      if (confirm(`¿Estás seguro de que deseas desconectar el cliente ${clientId}?`)) {
        // Simular una demora en la desconexión
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Actualizar el estado directamente
        setClientsStatus(prev => ({
          ...prev,
          [clientId]: {
            ...prev[clientId],
            status: 'disconnected',
            qrCode: null,
            qrExpiresAt: null,
            info: null
          }
        }));

        console.log(`Cliente ${clientId} desconectado con éxito (simulado)`);
      }
    } catch (error) {
      console.error('Error al desconectar cliente:', error);
    }
  };


  // Define los items del menú incluyendo el botón de conexión
  const menuItems = [
    {
      label: 'Dashboard',
      path: '/dashboard',
      icon: Home,
      roles: ['admin', 'user']
    },
    {
      label: 'Atención al Cliente',
      path: '/customer-service',
      icon: Phone,
      roles: ['admin', 'user', 'customer_service']
    },
    {
      label: 'Relación de Pagos',
      path: '/payment-relations',
      icon: DollarSign,
      roles: ['admin', 'payments']
    },
    {
      label: 'Documentos',
      path: '/documents',
      icon: FileText,
      roles: ['admin']
    },
    {
      label: 'Archivo',
      path: '/archivo',
      icon: FolderArchive,
      roles: ['admin']
    }
  ];

  return (
    <div className={`flex flex-col h-screen overflow-hidden transition-colors duration-300 ${
      isDarkMode
        ? 'bg-gradient-to-b from-slate-800 via-gray-800 to-pink-900'
        : 'bg-gradient-to-b from-slate-400 via-blue-300 to-pink-300'
    }`}>
      {/* Header superior con navegación */}
      <div className={`m-2 shadow-lg border transition-colors duration-300 ${
        isDarkMode ? 'bg-gray-800/50 border-gray-700' : 'bg-white/50 border-gray-200'
      }`} style={{ borderRadius: '5px' }}>
        {/* Fila única: Logo, navegación centrada y botones de acción */}
        <div className="flex items-center justify-between px-4 py-3">
          {/* Logo */}
          <div className="flex items-center">
            <img src={logoWifigo} alt="Logo Wifigo" className="h-8 sm:h-10 w-auto" />
          </div>

          {/* Navegación centrada */}
          <nav className="hidden md:flex items-center justify-center flex-1">
            <div className="flex items-center gap-1">
              {menuItems.map((item) => (
                (item.roles.includes(userRole) || item.roles.includes('*')) && (
                  <Link
                    key={item.path}
                    to={item.path}
                    className={`flex items-center px-3 py-2 mx-1 rounded-lg text-sm font-medium transition-colors ${
                      location.pathname === item.path
                        ? isDarkMode
                          ? 'bg-blue-600 text-white'
                          : 'bg-blue-100 text-blue-700'
                        : isDarkMode
                          ? 'text-gray-300 hover:bg-gray-700 hover:text-white'
                          : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
                    }`}
                  >
                    <item.icon className="w-4 h-4 mr-2" />
                    <span>{item.label}</span>
                  </Link>
                )
              ))}
            </div>
          </nav>

          {/* Usuario y botones de acción */}
          <div className="flex items-center gap-3">
            {/* Toggle de modo oscuro */}
            <button
              onClick={toggleDarkMode}
              className="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              style={{
                backgroundColor: isDarkMode ? '#3B82F6' : '#E5E7EB'
              }}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  isDarkMode ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
              {/* Iconos del sol y luna */}
              <span className="absolute left-1 top-1 text-xs">
                {isDarkMode ? '🌙' : '☀️'}
              </span>
            </button>

            {/* Dropdown del usuario */}
            {username && (
              <div className="relative">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setIsUserMenuOpen(!isUserMenuOpen);
                  }}
                  className={`flex items-center gap-2 rounded-lg px-3 py-2 transition-colors ${
                    isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'
                  }`}
                >
                  {/* Círculo con inicial */}
                  <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                    {getUserInitial(username)}
                  </div>
                  {/* Nombre del usuario */}
                  <span className={`text-sm font-medium hidden sm:inline ${
                    isDarkMode ? 'text-gray-300' : 'text-gray-700'
                  }`}>
                    {username}
                  </span>
                  {/* Flecha */}
                  <svg
                    className={`w-4 h-4 text-gray-500 transition-transform ${isUserMenuOpen ? 'rotate-180' : ''}`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>

                {/* Dropdown menu */}
                {isUserMenuOpen && (
                  <div
                    className={`absolute right-0 mt-2 w-48 rounded-lg shadow-lg border py-1 z-50 transition-colors duration-300 ${
                      isDarkMode
                        ? 'bg-gray-800 border-gray-600'
                        : 'bg-white border-gray-200'
                    }`}
                    onClick={(e) => e.stopPropagation()}
                  >
                    <button
                      onClick={handleLogout}
                      className={`w-full text-left px-4 py-2 text-sm flex items-center gap-2 transition-colors ${
                        isDarkMode
                          ? 'text-gray-300 hover:bg-gray-700'
                          : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      <LogOut className="w-4 h-4" />
                      Cerrar Sesión
                    </button>
                  </div>
                )}
              </div>
            )}

            {/* Botón de menú móvil */}
            <button
              className="md:hidden bg-gray-200 p-2 rounded-lg"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            >
              {isMobileMenuOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
            </button>
          </div>
        </div>

        {/* Menú móvil desplegable */}
        <div className={`${isMobileMenuOpen ? 'block' : 'hidden'} md:hidden border-t transition-colors duration-300 ${
          isDarkMode ? 'border-gray-700' : 'border-gray-200'
        }`}>
          <nav className="flex flex-col px-4 py-2">
            {menuItems.map((item) => (
              (item.roles.includes(userRole) || item.roles.includes('*')) && (
                <Link
                  key={item.path}
                  to={item.path}
                  onClick={() => setIsMobileMenuOpen(false)}
                  className={`flex items-center px-3 py-2 mx-1 rounded-lg text-sm font-medium transition-colors ${
                    location.pathname === item.path
                      ? isDarkMode
                        ? 'bg-blue-600 text-white'
                        : 'bg-blue-100 text-blue-700'
                      : isDarkMode
                        ? 'text-gray-300 hover:bg-gray-700 hover:text-white'
                        : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
                  }`}
                >
                  <item.icon className="w-4 h-4 mr-2" />
                  <span>{item.label}</span>
                </Link>
              )
            ))}
          </nav>
        </div>
      </div>

      {/* Modal de Conexión/Desconexión WhatsApp */}
      <Modal
        isOpen={isQRModalOpen}
        onRequestClose={() => setIsQRModalOpen(false)}
        className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-lg shadow-xl p-3 sm:p-4 md:p-6 w-[90%] sm:w-[80%] md:w-[70%] lg:w-[600px] max-h-[90vh] overflow-y-auto"
        overlayClassName="fixed inset-0 bg-black bg-opacity-50 z-50"
        shouldCloseOnOverlayClick={true}
      >
        <div className="flex justify-between items-center mb-3 sm:mb-4">
          <h2 className="text-lg sm:text-xl font-bold">
            Conectar Servicio de Mensajería
          </h2>
          <button
            onClick={() => setIsQRModalOpen(false)}
            className="text-gray-500 hover:text-gray-700 p-1"
          >
            ✕
          </button>
        </div>

        {error && (
          <div className="mb-3 sm:mb-4 p-2 sm:p-3 bg-red-100 text-red-700 rounded-lg text-xs sm:text-sm">
            {error}
          </div>
        )}

        <div className="mb-4 p-2 sm:p-3 bg-blue-100 text-blue-800 rounded-lg">
          <p className="font-medium text-sm sm:text-base">Estado de conexión del servicio:</p>
          <div className="mt-2">
            {Object.entries(clientsStatus).map(([clientId, client]) => (
              <div key={clientId} className={`flex items-center mb-2 p-1 sm:p-2 rounded-lg ${
                client.status === 'connected' ? 'bg-green-50 border border-green-200' :
                client.status === 'connecting' ? 'bg-yellow-50 border border-yellow-200' :
                'bg-gray-50 border border-gray-200'
              }`}>
                <div className={`w-2 sm:w-3 h-2 sm:h-3 rounded-full mr-1 sm:mr-2 ${
                  client.status === 'connected' ? 'bg-green-500' :
                  client.status === 'connecting' ? 'bg-yellow-500' :
                  'bg-gray-500'
                }`}></div>
                <div className="flex-1">
                  <p className="font-medium text-xs sm:text-sm">{clientId === 'client1' ? 'Cliente 1' : 'Cliente 2'}: {
                    client.status === 'connected' ? 'Conectado' :
                    client.status === 'connecting' ? 'Conectando...' :
                    'Desconectado'
                  }</p>
                  {client.status === 'connected' && client.info && (
                    <p className="text-xs sm:text-sm">
                      <span className="font-medium">Número:</span> <span className="font-bold">{client.info.number ? `+${client.info.number}` : 'No disponible'}</span>
                    </p>
                  )}
                  {client.status === 'connecting' && (
                    <p className="text-xs text-blue-600">
                      QR disponible: {client.qrCode ? 'SÍ' : 'NO'}
                      {client.qrCode && ` (${client.qrCode.length} caracteres)`}
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>
          <p className="text-xs sm:text-sm mt-2">Puedes conectar ambos clientes simultáneamente.</p>
          <div className="mt-2 text-xs text-gray-500">
            <p>Información de depuración:</p>
            <ul className="list-disc pl-4 text-xs">
              <li>client1 status: {clientsStatus.client1?.status}</li>
              <li>client1 QR: {clientsStatus.client1?.qrCode ? 'Disponible' : 'No disponible'}</li>
              <li>client2 status: {clientsStatus.client2?.status}</li>
              <li>client2 QR: {clientsStatus.client2?.qrCode ? 'Disponible' : 'No disponible'}</li>
            </ul>
          </div>
        </div>

        <div>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
            {Object.entries(clientsStatus).map(([clientId, client]) => (
              <div
                key={clientId}
                className={`border rounded-lg p-3 sm:p-4 ${
                  client.status === 'connected' ? 'bg-green-50' : ''
                } ${
                  client.status === 'error' ? 'bg-red-50' : ''
                }`}
              >
                <div className="flex justify-between items-center mb-2">
                  <h3 className="font-semibold flex items-center text-sm sm:text-base">
                    {clientId === 'client1' ? 'Cliente 1' : 'Cliente 2'}
                  </h3>
                  <span className={`text-xs sm:text-sm px-1 sm:px-2 py-1 rounded-full ${
                    client.status === 'connected'
                      ? 'bg-green-100 text-green-800'
                      : client.status === 'connecting'
                        ? 'bg-yellow-100 text-yellow-800'
                        : client.status === 'error'
                          ? 'bg-red-100 text-red-800'
                          : 'bg-gray-100 text-gray-800'
                  }`}>
                    {client.status === 'connected'
                      ? 'Conectado'
                      : client.status === 'connecting'
                        ? 'Conectando'
                        : client.status === 'error'
                          ? 'Error'
                          : 'Desconectado'
                    }
                  </span>
                </div>

                {client.error && (
                  <div className="text-xs text-red-500 mb-2">
                    Error: {client.error}
                  </div>
                )}

                {client.status === 'connected' && client.info && (
                  <div className="mt-2 p-3 bg-green-50 border border-green-200 rounded-lg">
                    <h4 className="font-semibold text-green-800 mb-1 text-sm sm:text-base">Dispositivo conectado:</h4>
                    <div className="text-xs sm:text-sm text-gray-700">
                      <p className="flex items-center mb-3">
                        <span className="font-medium mr-1">Número:</span>
                        <span className="text-sm sm:text-base font-bold text-green-700">
                          {client.info.number ? `+${client.info.number}` : 'No disponible'}
                        </span>
                      </p>
                      <button
                        onClick={() => disconnectClient(clientId)}
                        className="w-full bg-red-500 hover:bg-red-600 text-white py-1 px-2 sm:px-3 rounded-lg text-xs sm:text-sm flex items-center justify-center"
                      >
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                        </svg>
                        Desconectar
                      </button>
                    </div>
                  </div>
                )}

                {client.status === 'connecting' ? (
                  client.qrCode ? (
                    <div className="mt-3 flex flex-col items-center">
                      <p className="text-xs sm:text-sm text-gray-600 mb-2">Código QR para {clientId}:</p>
                      <div className="border-4 border-green-500 p-1 sm:p-2 bg-white">
                        <QRCodeSVG value={client.qrCode} size={120} className="w-[120px] h-[120px] sm:w-[150px] sm:h-[150px]" />
                      </div>
                      <p className="text-xs text-blue-500 mt-1">
                        QR Code ID: {clientId}
                      </p>
                      {client.qrExpiresAt && (
                        <p className="text-xs text-gray-500 mt-1 text-center px-2">
                          Este código QR expirará en {Math.max(0, Math.floor((client.qrExpiresAt - Date.now()) / 1000 / 60))} min y {Math.max(0, Math.floor((client.qrExpiresAt - Date.now()) / 1000 % 60))} seg
                        </p>
                      )}
                      <button
                        onClick={() => generateQRCode(clientId)}
                        className="mt-2 bg-blue-500 hover:bg-blue-600 text-white py-1 px-2 sm:px-3 rounded-lg text-xs sm:text-sm flex items-center"
                        disabled={loadingQR[clientId]}
                      >
                        {loadingQR[clientId] ? (
                          <>
                            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            <span>Regenerando...</span>
                          </>
                        ) : (
                          <>
                            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                            <span>Regenerar QR</span>
                          </>
                        )}
                      </button>
                    </div>
                  ) : (
                    <div className="mt-3 flex flex-col items-center">
                      <p className="text-sm text-gray-600 mb-2">Generando código QR para {clientId}...</p>
                      <div className="animate-pulse flex space-x-4">
                        <div className="rounded-full bg-gray-200 h-12 w-12"></div>
                      </div>
                      <button
                        onClick={() => generateQRCode(clientId)}
                        className="mt-4 bg-blue-500 hover:bg-blue-600 text-white py-1 px-3 rounded-lg text-sm flex items-center"
                        disabled={loadingQR[clientId]}
                      >
                        {loadingQR[clientId] ? (
                          <>
                            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            <span>Generando...</span>
                          </>
                        ) : (
                          <>
                            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                            <span>Regenerar QR</span>
                          </>
                        )}
                      </button>
                    </div>
                  )
                ) : client.status !== 'connected' && (
                  <div className="mt-3 flex flex-col items-center">
                    <button
                      onClick={() => generateQRCode(clientId)}
                      className="bg-green-500 hover:bg-green-600 text-white py-1 sm:py-2 px-3 sm:px-4 rounded-lg flex items-center text-xs sm:text-sm"
                      disabled={loadingQR[clientId]}
                    >
                      {loadingQR[clientId] ? (
                        <>
                          <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          <span>Generando código QR...</span>
                        </>
                      ) : (
                        <>
                          <Smartphone className="w-5 h-5 mr-2" />
                          <span>Conectar {clientId === 'client1' ? '1' : '2'}</span>
                        </>
                      )}
                    </button>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </Modal>

      {/* Contenido principal */}
      <div className={`flex-1 overflow-y-auto transition-colors duration-300 ${
        isDarkMode ? 'bg-gray-900' : 'bg-gray-100'
      }`}>
        {children}
      </div>
    </div>
  );
}

export default Layout









