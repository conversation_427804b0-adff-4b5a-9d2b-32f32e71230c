const express = require('express');
const { createServer } = require('http');
const { Server } = require('socket.io');
const path = require('path');
const dotenv = require('dotenv');
const fs = require('fs');
const axios = require('axios');

// Determinar qué archivo .env cargar según el entorno
const NODE_ENV = process.env.NODE_ENV || 'development';
const envFile = NODE_ENV === 'production' ? '.env.production' : '.env.development';

// Verificar si el archivo de entorno existe
const envPath = path.resolve(__dirname, envFile);
if (fs.existsSync(envPath)) {
  console.log(`Cargando variables de entorno desde ${envFile}`);
  dotenv.config({ path: envPath });
} else {
  console.log(`Archivo ${envFile} no encontrado, usando .env por defecto`);
  dotenv.config();
}

const app = express();
const httpServer = createServer(app);
// Configuración de variables de entorno
const FRONTEND_URL = process.env.FRONTEND_URL || 'https://central.wifigo.com.mx';
const WEBHOOK_URL = process.env.WEBHOOK_URL || 'https://aawebhook.wifigo.com.mx/webhook/enviar-mensaje';

// Ya no almacenamos mensajes en memoria, solo los emitimos

const io = new Server(httpServer, {
  cors: {
    origin: "*", // Permitir conexiones desde cualquier origen
    methods: ["GET", "POST"],
    credentials: true
  }
});

// Almacenamiento temporal en memoria para sender_instance por conversación
const lastSenderInstanceMemory = {};

// Middleware
app.use(express.json({ limit: '50mb' }));

// Middleware CORS para Express
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*'); // Permitir conexiones desde cualquier origen
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Credentials', 'true');
  next();
});

// Ruta raíz - Página de bienvenida
app.get('/', (req, res) => {
  const html = `
    <!DOCTYPE html>
    <html lang="es">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>API WhatsApp - Wifigo</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                margin: 0;
                padding: 0;
                background: linear-gradient(135deg, #25D366 0%, #128C7E 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .container {
                background: white;
                padding: 2rem;
                border-radius: 15px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                text-align: center;
                max-width: 500px;
                margin: 20px;
            }
            .logo {
                font-size: 3rem;
                margin-bottom: 1rem;
            }
            h1 {
                color: #128C7E;
                margin-bottom: 1rem;
                font-size: 2rem;
            }
            .status {
                background: #25D366;
                color: white;
                padding: 1rem;
                border-radius: 10px;
                font-size: 1.2rem;
                font-weight: bold;
                margin: 1rem 0;
            }
            .info {
                color: #666;
                margin: 1rem 0;
                line-height: 1.6;
            }
            .endpoints {
                background: #f8f9fa;
                padding: 1rem;
                border-radius: 8px;
                margin: 1rem 0;
                text-align: left;
            }
            .endpoint {
                font-family: monospace;
                background: #e9ecef;
                padding: 0.5rem;
                margin: 0.5rem 0;
                border-radius: 4px;
                font-size: 0.9rem;
            }
            .footer {
                margin-top: 2rem;
                color: #999;
                font-size: 0.9rem;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="logo">📱💬</div>
            <h1>API WhatsApp - Wifigo</h1>
            <div class="status">✅ API Funcionando Correctamente</div>
            <div class="footer">
                <p>© 2025 Wifigo - Sistema de Atención al Cliente</p>
                <p>Última actualización: ${new Date().toLocaleString('es-MX')}</p>
            </div>
        </div>
        <script>
            // Actualizar el contador de clientes cada 5 segundos
            setInterval(() => {
                fetch('/test-socket')
                    .then(response => response.json())
                    .then(data => {
                        document.getElementById('clients').textContent = data.connectedClients || 0;
                    })
                    .catch(() => {
                        document.getElementById('clients').textContent = 'Error';
                    });
            }, 5000);
        </script>
    </body>
    </html>
  `;
  res.send(html);
});

// Estado global de operadores y sus chats seleccionados
let operatorSelections = {};
// Mapeo de socket IDs a operadores para limpieza
let socketToOperator = {};

// Estado global de usuarios conectados
let connectedUsers = {};
// Mapeo de socket IDs a usuarios
let socketToUser = {};

// Estado global de conversaciones atendidas (AGREGADO)
let globalConversationAttended = {};
// Mapeo de operadores a su conversación actual atendida (AGREGADO)
let operatorCurrentConversation = {};

// Socket.IO events
io.on('connection', (socket) => {
  console.log('Cliente conectado con socket ID:', socket.id);

  // Enviar el estado actual de selecciones a todos los clientes
  socket.emit('operator-selections-sync', operatorSelections);

  // Enviar la lista actual de usuarios conectados
  socket.emit('connected-users-sync', connectedUsers);

  // Enviar el estado actual de conversaciones atendidas (AGREGADO)
  socket.emit('operator-attended-sync', globalConversationAttended);

  // Manejar cuando un operador selecciona un chat
  socket.on('operator-select-chat', (data) => {
    const { operatorId, operatorName, chatId } = data;

    console.log(`Operador ${operatorName} (${operatorId}) seleccionó chat: ${chatId}`);

    // Guardar la asociación socket-operador para limpieza posterior
    socketToOperator[socket.id] = operatorId;

    // Actualizar el estado global
    operatorSelections[operatorId] = {
      chatId,
      operatorName,
      timestamp: Date.now(),
      socketId: socket.id
    };

    // Emitir a todos los clientes conectados el estado actualizado
    io.emit('operator-selections-update', operatorSelections);
  });

  // Manejar cuando un operador se desconecta o limpia su selección
  socket.on('operator-clear-selection', (data) => {
    const { operatorId } = data;

    console.log(`Limpiando selección del operador: ${operatorId}`);

    // Eliminar la selección del operador
    delete operatorSelections[operatorId];
    delete socketToOperator[socket.id];

    // Emitir a todos los clientes conectados el estado actualizado
    io.emit('operator-selections-update', operatorSelections);
  });

  // Manejar cuando un usuario inicia sesión
  socket.on('user-login', (data) => {
    const { userId, userName, userEmail } = data;

    console.log(`Usuario ${userName} (${userId}) inició sesión`);

    // Guardar la asociación socket-usuario
    socketToUser[socket.id] = userId;

    // Actualizar el estado global de usuarios conectados
    connectedUsers[userId] = {
      socketId: socket.id,
      name: userName,
      email: userEmail,
      loginTime: new Date().toISOString(),
      lastActivity: new Date().toISOString(),
      status: 'online'
    };

    // Emitir a todos los clientes conectados la lista actualizada
    io.emit('connected-users-update', connectedUsers);
  });

  // Manejar cuando un usuario actualiza su actividad
  socket.on('user-activity', (data) => {
    const { userId } = data;

    if (connectedUsers[userId]) {
      connectedUsers[userId].lastActivity = new Date().toISOString();

      // Emitir actualización (opcional, para no saturar)
      // io.emit('connected-users-update', connectedUsers);
    }
  });

  // Manejar cuando un usuario cierra sesión manualmente
  socket.on('user-logout', (data) => {
    const { userId } = data;

    console.log(`Usuario ${connectedUsers[userId]?.name || userId} cerró sesión`);

    // Eliminar del estado global
    delete connectedUsers[userId];
    delete socketToUser[socket.id];

    // Emitir a todos los clientes conectados la lista actualizada
    io.emit('connected-users-update', connectedUsers);
  });

  // Escuchar cuando un operador cambia de conversación (marca una nueva y desmarca la anterior)
  socket.on('switch-conversation-attended', (data) => {
    const { newChatId, operatorId } = data;
    console.log(`🔄 Operador ${operatorId} cambió a conversación ${newChatId}`);

    // Buscar si el operador tenía una conversación anterior marcada
    const previousChatId = operatorCurrentConversation[operatorId];

    // Actualizar el estado global de forma atómica
    if (previousChatId && previousChatId !== newChatId) {
      console.log(`❌ Desmarcando conversación anterior ${previousChatId} del operador ${operatorId}`);
      delete globalConversationAttended[previousChatId];
    }

    console.log(`✅ Marcando nueva conversación ${newChatId} del operador ${operatorId}`);
    globalConversationAttended[newChatId] = {
      attended: true,
      operatorId: operatorId,
      timestamp: new Date().toISOString()
    };

    // Actualizar el mapeo del operador
    operatorCurrentConversation[operatorId] = newChatId;

    // Emitir UN SOLO evento con toda la información del cambio
    io.emit('conversation-switch-complete', {
      operatorId,
      previousChatId: previousChatId || null,
      newChatId,
      timestamp: new Date().toISOString(),
      globalState: globalConversationAttended
    });

    console.log(`🎯 Cambio completado: ${previousChatId || 'ninguna'} → ${newChatId}`);
  });

  // Escuchar cuando un operador se desconecta o desmarca su conversación actual
  socket.on('unmark-current-conversation', (data) => {
    const { operatorId } = data;
    console.log(`Operador ${operatorId} desmarcó su conversación actual`);

    // Buscar la conversación actual del operador
    const currentChatId = operatorCurrentConversation[operatorId];

    if (currentChatId) {
      // Desmarcar la conversación
      delete globalConversationAttended[currentChatId];
      delete operatorCurrentConversation[operatorId];

      // Emitir actualización
      io.emit('conversation-attended-update', {
        chatId: currentChatId,
        attended: false,
        operatorId,
        timestamp: new Date().toISOString()
      });
    }
  });

  // Manejar cuando un operador atiende una conversación (AGREGADO)
  socket.on('operator-attend-chat', (data) => {
    const { operatorId, operatorName, chatId, timestamp } = data;
    console.log(`🎯 Operador ${operatorName} (${operatorId}) atiende conversación: ${chatId}`);

    // Verificar si el operador ya tenía otra conversación atendida
    const previousChatId = operatorCurrentConversation[operatorId];

    if (previousChatId && previousChatId !== chatId) {
      console.log(`❌ Desmarcando conversación anterior ${previousChatId} del operador ${operatorId}`);
      delete globalConversationAttended[previousChatId];
    }

    // Marcar la nueva conversación como atendida
    globalConversationAttended[chatId] = {
      attended: true,
      operatorId: operatorId,
      operatorName: operatorName,
      timestamp: timestamp
    };

    // Actualizar el mapeo del operador
    operatorCurrentConversation[operatorId] = chatId;

    // Emitir actualización a todos los clientes conectados
    io.emit('operator-attended-update', globalConversationAttended);

    console.log(`✅ Conversación ${chatId} marcada como atendida por ${operatorName}`);
  });

  // Manejar cuando un operador deja de atender una conversación (AGREGADO)
  socket.on('operator-unattend-chat', (data) => {
    const { operatorId, chatId } = data;
    console.log(`🔄 Operador ${operatorId} deja de atender conversación: ${chatId}`);

    // Desmarcar la conversación
    delete globalConversationAttended[chatId];
    delete operatorCurrentConversation[operatorId];

    // Emitir actualización a todos los clientes conectados
    io.emit('operator-attended-update', globalConversationAttended);

    console.log(`❌ Conversación ${chatId} desmarcada como atendida`);
  });

  // Manejar solicitud de sincronización inicial de conversaciones atendidas (AGREGADO)
  socket.on('request-operator-attended-sync', () => {
    console.log('📡 Cliente solicita sincronización de conversaciones atendidas');
    socket.emit('operator-attended-sync', globalConversationAttended);
  });

  socket.on('disconnect', () => {
    console.log('Cliente desconectado:', socket.id);

    // Limpiar la selección del operador que se desconectó
    const operatorId = socketToOperator[socket.id];
    if (operatorId) {
      console.log(`Limpiando selección del operador desconectado: ${operatorId}`);
      delete operatorSelections[operatorId];
      delete socketToOperator[socket.id];

      // Emitir a todos los clientes conectados el estado actualizado
      io.emit('operator-selections-update', operatorSelections);

      // Limpiar conversación atendida del operador desconectado (AGREGADO)
      const attendedChatId = operatorCurrentConversation[operatorId];
      if (attendedChatId) {
        console.log(`Limpiando conversación atendida ${attendedChatId} del operador desconectado: ${operatorId}`);
        delete globalConversationAttended[attendedChatId];
        delete operatorCurrentConversation[operatorId];

        // Emitir actualización a todos los clientes conectados
        io.emit('operator-attended-update', globalConversationAttended);
      }
    }

    // Limpiar el usuario que se desconectó
    const userId = socketToUser[socket.id];
    if (userId) {
      console.log(`Usuario desconectado: ${connectedUsers[userId]?.name || userId}`);
      delete connectedUsers[userId];
      delete socketToUser[socket.id];

      // Emitir a todos los clientes conectados la lista actualizada
      io.emit('connected-users-update', connectedUsers);
    }

    console.log('Total de clientes conectados:', io.sockets.sockets.size);
  });
});

// API routes - Recibir mensajes de WhatsApp
app.post('/api/webhook', (req, res) => {
  try {
    const payload = req.body;

    // Extraer datos del payload con el nuevo formato
    const number = payload.number || 'desconocido';
    const texto = payload.mensaje || '';
    const url = payload.url || null;
    const tipoMensaje = payload.tipo_mensaje || 'conversation';
    const origen = payload.origen === true;

    // Determinar el caption según el tipo de mensaje
    let caption = '';
    if (tipoMensaje === 'imageMessage' && payload.mensaje_imagen) {
      caption = payload.mensaje_imagen;
    } else if (tipoMensaje === 'documentMessage' && payload.mensaje_documento) {
      caption = payload.mensaje_documento;
    }

    // Generar timestamp en formato ISO
    const timestamp = new Date().toISOString();

    // Crear mensaje en formato estándar
    const standardMessage = {
      conversation_id: number,
      text_content: texto,
      media_url: url,
      media_caption: caption,
      message_type: tipoMensaje,
      sender_instance: number,
      origin: origen
    };

    // Crear mensaje en formato compatible con el frontend
    const msg = {
      from: number,
      body: texto,
      timestamp: timestamp,
      direction: origen ? 'out' : 'in',
      media_url: url,
      media_type: determinarTipoMedia(tipoMensaje, url),
      caption: caption,
      message_type: tipoMensaje,
      // Agregar los campos originales en formato estándar para compatibilidad
      supabase_data: standardMessage
    };

    // No almacenamos mensajes localmente, solo los emitimos

    // Emitir mensaje al frontend
    io.emit('new-message', msg);

    return res.status(200).json({ mensaje: 'Mensaje recibido y procesado' });
  } catch (err) {
    console.error('Error al procesar el webhook:', err);
    return res.status(500).json({ error: 'Error al procesar el webhook' });
  }
});

// Función auxiliar para determinar el tipo de media
function determinarTipoMedia(tipoMensaje, url) {
  if (!url) return null;

  if (tipoMensaje === 'imageMessage') {
    return 'image/jpeg';
  } else if (tipoMensaje === 'audioMessage') {
    return 'audio/ogg';
  } else if (tipoMensaje === 'documentMessage') {
    // Intentar determinar el tipo por la extensión
    if (url.toLowerCase().endsWith('.pdf')) return 'application/pdf';
    if (url.toLowerCase().endsWith('.doc') || url.toLowerCase().endsWith('.docx')) return 'application/msword';
    if (url.toLowerCase().endsWith('.xls') || url.toLowerCase().endsWith('.xlsx')) return 'application/vnd.ms-excel';
    if (url.toLowerCase().endsWith('.csv')) return 'text/csv';
    return 'application/octet-stream';
  } else if (tipoMensaje === 'videoMessage') {
    return 'video/mp4';
  }

  return 'application/octet-stream';
}

// Endpoint para enviar mensajes
app.post('/send-message', async (req, res) => {
  try {
    console.log('========== INICIO DE SOLICITUD /send-message ==========');
    console.log('Cuerpo de la solicitud:', req.body);

    // Extraer los campos directamente
    const { destinatario, mensaje, remote_jid, sender_instance } = req.body;

    console.log('Campos extraídos:', { destinatario, mensaje, remote_jid, sender_instance });

    // Verificar si el cuerpo de la solicitud está vacío
    if (!req.body || Object.keys(req.body).length === 0) {
      console.error('Error: El cuerpo de la solicitud está vacío');
      return res.status(400).json({
        error: 'El cuerpo de la solicitud está vacío'
      });
    }

    // Verificar si faltan campos requeridos
    if (!destinatario || !mensaje) {
      console.error('Error: Faltan campos requeridos', { destinatario, mensaje });
      return res.status(400).json({
        error: 'Se requiere destinatario y mensaje',
        received: req.body
      });
    }

    console.log('Campos extraídos correctamente:', { destinatario, mensaje, remote_jid, sender_instance });

    // Responder inmediatamente al cliente
    res.json({ success: true, message: 'Mensaje enviado al webhook' });

    // Enviar el mensaje al webhook externo con los datos tal cual
    try {
      // Asegurar que sender_instance tenga un valor válido
      console.log('Valor original de sender_instance:', sender_instance, 'Tipo:', typeof sender_instance);
      const finalSenderInstance = sender_instance || 'default-instance';
      console.log('Valor final de sender_instance:', finalSenderInstance);

      const webhookPayload = {
        id: destinatario,           // ID del chat
        remote_jid: remote_jid,     // remote_jid del chat
        mensaje: mensaje,           // Contenido del mensaje
        origen: true,               // Indicar que es un mensaje saliente
        tipo_mensaje: 'conversation', // Tipo de mensaje
        sender_instance: finalSenderInstance // sender_instance o valor por defecto
      };

      console.log('Enviando al webhook:', JSON.stringify(webhookPayload, null, 2));

      await axios.post(WEBHOOK_URL, webhookPayload);
      console.log(`Mensaje enviado al webhook para id=${destinatario}, remote_jid=${remote_jid}`);
    } catch (webhookError) {
      console.error('Error al enviar mensaje al webhook:', webhookError);
      // No fallamos la respuesta al cliente, solo registramos el error
    }
  } catch (error) {
    console.error('Error al enviar mensaje:', error);
    // No enviamos respuesta de error porque ya respondimos al cliente
  }
});

// Endpoint para enviar archivos
app.post('/send-file', async (req, res) => {
  try {
    const { to, file, clientId, caption } = req.body;

    if (!to || !file || !file.data || !file.mimetype) {
      return res.status(400).json({ error: 'Se requiere destinatario y archivo válido' });
    }

    // Usar el número tal cual como identificador
    const chatId = to;

    // Determinar el tipo de mensaje basado en el mimetype
    let tipoMensaje = 'documentMessage';
    if (file.mimetype.startsWith('image/')) {
      tipoMensaje = 'imageMessage';
    } else if (file.mimetype.startsWith('audio/')) {
      tipoMensaje = 'audioMessage';
    } else if (file.mimetype.startsWith('video/')) {
      tipoMensaje = 'videoMessage';
    }

    // Crear mensaje en formato estándar
    const standardMessage = {
      conversation_id: chatId,
      text_content: '',
      media_url: null, // Se establecerá cuando el webhook externo procese el archivo
      media_caption: caption || '',
      message_type: tipoMensaje,
      sender_instance: chatId,
      origin: true,
      // Mantener la información del archivo para compatibilidad
      media: {
        data: file.data,
        mimetype: file.mimetype,
        filename: file.filename || 'archivo'
      }
    };

    // Crear el mensaje en formato compatible con el frontend
    const timestamp = new Date().toISOString();
    const newMessage = {
      from: chatId,
      body: '',
      timestamp,
      direction: 'out',
      client: clientId || 'default',
      media_url: null, // Se establecerá cuando el webhook externo procese el archivo
      media_type: file.mimetype,
      caption: caption || '',
      message_type: tipoMensaje,
      // Mantener la información del archivo para compatibilidad
      media: {
        data: file.data,
        mimetype: file.mimetype,
        filename: file.filename || 'archivo'
      },
      // Agregar los campos originales en formato estándar para compatibilidad
      supabase_data: standardMessage
    };

    // No almacenamos mensajes localmente, solo los emitimos

    // Emitir el mensaje a través de Socket.IO
    io.emit('new-message', newMessage);

    // Enviar el archivo al webhook externo con el ID remoto exactamente como se recibió
    try {
      // Adaptar al nuevo formato
      const webhookPayload = {
        number: to,
        mensaje: '',
        origen: true,
        tipo_mensaje: tipoMensaje
      };

      // Agregar el caption según el tipo de mensaje
      if (tipoMensaje === 'imageMessage') {
        webhookPayload.mensaje_imagen = caption || '';
      } else if (tipoMensaje === 'documentMessage') {
        webhookPayload.mensaje_documento = caption || '';
      }

      // Agregar los datos del archivo
      webhookPayload.media = {
        data: file.data,
        mimetype: file.mimetype,
        filename: file.filename || 'archivo'
      };

      await axios.post(WEBHOOK_URL, webhookPayload);
      console.log(`Archivo enviado al webhook para ${to}`);
    } catch (webhookError) {
      console.error('Error al enviar archivo al webhook:', webhookError);
      // No fallamos la respuesta al cliente, solo registramos el error
    }

    res.json({ success: true, message: 'Archivo enviado' });
  } catch (error) {
    console.error('Error al enviar archivo:', error);
    res.status(500).json({ error: 'Error al enviar archivo' });
  }
});

// Endpoint para recuperar historial (deshabilitado)
app.get('/history', (_req, res) => {
  res.json({}); // Devolver un objeto vacío ya que no almacenamos mensajes localmente
});

// Endpoint para ocultar un chat específico (deshabilitado)
app.delete('/hide-chat/:chatId', (req, res) => {
  const { chatId } = req.params;

  // Emitir evento para notificar al frontend
  io.emit('chat-hidden', { chatId });

  console.log(`Chat ocultado: ${chatId}`);
  res.json({ success: true, message: 'Chat ocultado correctamente' });
});

// Endpoint para eliminar un chat específico (deshabilitado, mantener por compatibilidad)
app.delete('/delete-chat/:chatId', (req, res) => {
  const { chatId } = req.params;

  // Emitir evento para notificar al frontend
  io.emit('chat-hidden', { chatId });

  console.log(`Chat ocultado: ${chatId}`);
  res.json({ success: true, message: 'Chat ocultado correctamente' });
});

// Endpoint para obtener información sobre los mensajes de un número específico (deshabilitado)
app.get('/chat-info/:phone', (req, res) => {
  const phone = req.params.phone;

  // Devolver información simulada ya que no almacenamos mensajes localmente
  res.json({
    phone,
    totalMessages: 0,
    incomingMessages: 0,
    lastIncomingMessage: null,
    clientToRespond: 'default'
  });
});





// Endpoint para obtener el estado de los clientes (simulado para compatibilidad)
app.get('/clients-status', (_req, res) => {
  res.json({
    default: {
      status: 'connected',
      info: {
        name: 'Sistema',
        number: 'system'
      }
    }
  });
});

// Endpoint de prueba para verificar la conexión Socket.IO
app.get('/test-socket', (_req, res) => {
  // Verificar si hay clientes conectados
  const connectedClients = io.sockets.sockets.size;
  console.log(`Clientes conectados: ${connectedClients}`);

  // Mostrar información detallada sobre los clientes conectados
  console.log('Información de clientes conectados:');
  io.sockets.sockets.forEach((socket, id) => {
    console.log(`Socket ID: ${id}, Conectado: ${socket.connected}`);
  });

  // Emitir un mensaje de prueba a todos los clientes
  const testMessage = {
    id: `test-${Date.now()}`,
    text: `Mensaje de prueba enviado a las ${new Date().toLocaleTimeString()}`
  };

  io.emit('test-connection', testMessage);
  console.log('Mensaje de prueba emitido a todos los clientes:', testMessage);

  res.json({
    success: true,
    connectedClients,
    message: 'Mensaje de prueba emitido a todos los clientes'
  });
});

// Endpoint de prueba para la sincronización de conversaciones atendidas
app.post('/test-conversation-attended', (req, res) => {
  try {
    const { chatId, operatorId, action, newChatId } = req.body;

    if (!operatorId) {
      return res.status(400).json({
        error: 'Se requiere operatorId'
      });
    }

    console.log(`Prueba de conversación atendida: ${action} para operador ${operatorId}`);

    if (action === 'switch' && newChatId) {
      // Simular cambio de conversación
      const previousChatId = operatorCurrentConversation[operatorId];

      if (previousChatId && previousChatId !== newChatId) {
        // Desmarcar la conversación anterior
        delete globalConversationAttended[previousChatId];

        // Emitir actualización para desmarcar
        io.emit('conversation-attended-update', {
          chatId: previousChatId,
          attended: false,
          operatorId,
          timestamp: new Date().toISOString()
        });
      }

      // Marcar la nueva conversación
      globalConversationAttended[newChatId] = {
        attended: true,
        operatorId: operatorId,
        timestamp: new Date().toISOString()
      };

      // Actualizar el mapeo del operador
      operatorCurrentConversation[operatorId] = newChatId;

      // Emitir actualización para marcar
      io.emit('conversation-attended-update', {
        chatId: newChatId,
        attended: true,
        operatorId,
        timestamp: globalConversationAttended[newChatId].timestamp
      });

      console.log(`Operador ${operatorId} cambió de ${previousChatId || 'ninguna'} a ${newChatId}`);

    } else if (action === 'unmark') {
      // Simular desmarcar conversación actual del operador
      const currentChatId = operatorCurrentConversation[operatorId];

      if (currentChatId) {
        delete globalConversationAttended[currentChatId];
        delete operatorCurrentConversation[operatorId];

        io.emit('conversation-attended-update', {
          chatId: currentChatId,
          attended: false,
          operatorId,
          timestamp: new Date().toISOString()
        });

        console.log(`Operador ${operatorId} desmarcó conversación ${currentChatId}`);
      }
    }

    res.json({
      success: true,
      message: `Acción ${action} ejecutada exitosamente`,
      globalState: globalConversationAttended,
      operatorMappings: operatorCurrentConversation
    });

  } catch (error) {
    console.error('Error en prueba de conversación atendida:', error);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// Endpoint para obtener el estado actual de conversaciones atendidas
app.get('/conversation-attended-state', (_req, res) => {
  res.json({
    success: true,
    globalState: globalConversationAttended,
    operatorMappings: operatorCurrentConversation,
    connectedClients: io.sockets.sockets.size
  });
});

// Endpoint para limpiar la conversación de un operador (usado en beforeunload)
app.post('/api/unmark-operator-conversation', (req, res) => {
  try {
    const { operatorId } = req.body;

    if (!operatorId) {
      return res.status(400).json({ error: 'Se requiere operatorId' });
    }

    console.log(`Limpiando conversación del operador ${operatorId} (beforeunload)`);

    // Buscar la conversación actual del operador
    const currentChatId = operatorCurrentConversation[operatorId];

    if (currentChatId) {
      // Desmarcar la conversación
      delete globalConversationAttended[currentChatId];
      delete operatorCurrentConversation[operatorId];

      // Emitir actualización a todos los clientes conectados
      io.emit('conversation-attended-update', {
        chatId: currentChatId,
        attended: false,
        operatorId,
        timestamp: new Date().toISOString()
      });

      console.log(`Conversación ${currentChatId} desmarcada para operador ${operatorId}`);
    }

    res.json({ success: true });
  } catch (error) {
    console.error('Error al limpiar conversación del operador:', error);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// Endpoint para enviar mensaje de prueba específico
app.post('/send-test-message', (req, res) => {
  try {
    console.log('Enviando mensaje de prueba específico...');

    // Mensaje de prueba con el formato exacto solicitado
    const testMessageData = {
      conversation_id: "a3f1c2d4-e5b6-47a8-b9c0-1234567890ab",
      text_content: "¡Hola!",
      media_url: null,
      media_caption: null,
      message_type: "textMessage",
      sender_instance: "instancia-A",
      origin: false
    };

    console.log('Datos del mensaje de prueba:', testMessageData);

    // Crear mensaje en formato compatible con el frontend
    const message = {
      from: testMessageData.conversation_id,
      body: testMessageData.text_content,
      timestamp: new Date().toISOString(),
      direction: testMessageData.origin ? 'out' : 'in',
      media_url: testMessageData.media_url,
      media_type: determinarTipoMedia(testMessageData.message_type, testMessageData.media_url),
      caption: testMessageData.media_caption || '',
      message_type: testMessageData.message_type,
      // Agregar los campos originales para compatibilidad
      supabase_data: testMessageData
    };

    console.log('Mensaje formateado para frontend:', message);

    // Verificar si hay clientes conectados
    const connectedClients = io.sockets.sockets.size;
    console.log(`Clientes conectados: ${connectedClients}`);

    if (connectedClients === 0) {
      console.warn('No hay clientes conectados para recibir el mensaje de prueba');
      return res.status(200).json({
        success: true,
        message: 'Mensaje de prueba creado pero no hay clientes conectados',
        connectedClients: 0,
        testMessage: message
      });
    }

    // Emitir el mensaje a todos los clientes
    io.emit('new-message', message);
    console.log('Mensaje de prueba emitido con evento new-message a todos los clientes');

    return res.status(200).json({
      success: true,
      message: 'Mensaje de prueba enviado exitosamente',
      connectedClients,
      testMessage: message
    });
  } catch (error) {
    console.error('Error al enviar mensaje de prueba:', error);
    return res.status(500).json({ error: 'Error al enviar mensaje de prueba' });
  }
});

// Endpoint para recibir mensajes de Supabase
app.post('/new-message', (req, res) => {
  try {
    console.log('Recibido mensaje de Supabase:', req.body);

    // Extraer datos del mensaje de Supabase
    const {
      conversation_id,
      text_content,
      media_url,
      media_caption,
      message_type,
      sender_instance,
      origin,
      from, // Permitir que el cliente envíe el campo 'from' directamente
      remote_jid // Extraer remote_jid del request body
    } = req.body;

    if (!conversation_id) {
      return res.status(400).json({ error: 'Se requiere conversation_id' });
    }

    // Crear mensaje en formato compatible con el frontend
    const message = {
      // Usar 'remote_jid' como from (quien envía el mensaje)
      from: remote_jid,
      body: text_content || '',
      timestamp: new Date().toISOString(),
      direction: origin ? 'out' : 'in',
      media_url: media_url || null,
      media_type: determinarTipoMedia(message_type, media_url),
      caption: media_caption || '',
      message_type: message_type || 'text',
      sender_instance: sender_instance, // Incluir sender_instance en el mensaje
      // Agregar los campos originales de Supabase para compatibilidad
      supabase_data: {
        conversation_id,
        text_content,
        media_url,
        media_caption,
        message_type,
        sender_instance,
        origin,
        remote_jid // Incluir remote_jid en supabase_data
      }
    };

    // Emitir el mensaje a través de Socket.IO
    console.log('Emitiendo mensaje de Supabase:', message);

    // Emitir el mensaje a través de Socket.IO solo con el evento new-message
    console.log('Emitiendo mensaje con evento new-message:', JSON.stringify(message).substring(0, 200));

    // Verificar si hay clientes conectados
    const connectedClients = io.sockets.sockets.size;
    console.log(`Clientes conectados: ${connectedClients}`);

    // Mostrar información detallada sobre los clientes conectados
    console.log('Información de clientes conectados:');
    io.sockets.sockets.forEach((socket, id) => {
      console.log(`Socket ID: ${id}, Conectado: ${socket.connected}`);
    });

    // Emitir el mensaje a todos los clientes
    io.emit('new-message', message);
    console.log('Mensaje emitido con evento new-message a todos los clientes');

    return res.status(200).json({ success: true, message: 'Mensaje procesado y emitido' });
  } catch (error) {
    console.error('Error al procesar mensaje de Supabase:', error);
    return res.status(500).json({ error: 'Error al procesar mensaje' });
  }
});

const PORT = process.env.PORT || 3002; // Cambiado a 3003 para evitar conflictos
httpServer.listen(PORT, () => {
  console.log(`Servidor backend iniciado en el puerto ${PORT}`);
  console.log(`Entorno: ${NODE_ENV}`);
  console.log(`Aceptando conexiones desde: ${FRONTEND_URL}`);
  console.log(`Webhook configurado en: ${WEBHOOK_URL}`);
  console.log(`Para acceder al servidor: http://localhost:${PORT}`);
});
