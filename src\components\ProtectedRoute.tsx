import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedRoute = ({ children }: ProtectedRouteProps) => {
  const location = useLocation();
  const userStr = localStorage.getItem('user');
  const user = userStr ? JSON.parse(userStr) : null;

  // Si no hay usuario, redirigir al login
  if (!user) {
    return <Navigate to="/" state={{ from: location }} replace />;
  }

  // Comprobar si la ruta actual requiere un rol específico
  const currentPath = location.pathname;

  // Definir las rutas y los roles permitidos para cada una
  // Este objeto se puede expandir fácilmente para incluir nuevas rutas y roles
  const routePermissions: Record<string, string[]> = {
    '/dashboard': ['admin'],
    '/payment-relations': ['admin', 'payments'],
    '/documents': ['admin'],
    '/customer-service': ['admin', 'customer_service'],
    '/archivo': ['admin']
    // Puedes añadir más rutas y roles aquí sin modificar el código principal
  };

  // Si la ruta actual tiene restricciones y el usuario no tiene el rol adecuado
  if (currentPath !== '/' &&
      routePermissions[currentPath as keyof typeof routePermissions] &&
      !routePermissions[currentPath as keyof typeof routePermissions].includes(user.role)) {

      // Mapeo de roles a rutas por defecto
    const defaultRoutesByRole: Record<string, string> = {
      'admin': '/dashboard',
      'customer_service': '/customer-service',
      'payments': '/payment-relations',
      // Puedes añadir más roles y rutas aquí sin modificar el código principal
    };

    // Obtener la ruta por defecto para el rol del usuario o redirigir al login
    const defaultRoute = defaultRoutesByRole[user.role] || '/';

    console.log(`Usuario con rol ${user.role} no tiene acceso a ${currentPath}, redirigiendo a ${defaultRoute}`);
    return <Navigate to={defaultRoute} replace />;
  }

  return <>{children}</>;
};

export default ProtectedRoute;


