// src/socket.js
import { io } from 'socket.io-client';

// Usar la URL del servidor desde las variables de entorno o fallback a localhost
const SOCKET_URL = import.meta.env.VITE_SOCKET_URL || 'http://localhost:3002';

console.log('Conectando al servidor Socket.IO en:', SOCKET_URL);

// Determinar si usar HTTPS basado en la URL
const isProduction = SOCKET_URL.startsWith('https://');

// Inicializa el socket apuntando al servidor
export const socket = io(SOCKET_URL, {
  path: '/socket.io',
  transports: ['websocket', 'polling'], // Permitir fallback a polling si websocket falla
  secure: isProduction, // true para HTTPS, false para localhost
  reconnection: true,
  reconnectionAttempts: Infinity, // Intentar reconectar indefinidamente
  reconnectionDelay: 1000,
  reconnectionDelayMax: 5000,
  timeout: 20000,
  autoConnect: true // Asegurarse de que se conecte automáticamente
});

// Agregar manejadores de eventos para monitorear la conexión
socket.on('connect', () => {
  console.log('Socket conectado exitosamente');
});

socket.on('disconnect', (reason) => {
  console.log(`Socket desconectado: ${reason}`);
});

socket.on('reconnect', (attemptNumber) => {
  console.log(`Socket reconectado después de ${attemptNumber} intentos`);
});

socket.on('reconnect_attempt', (attemptNumber) => {
  console.log(`Intento de reconexión #${attemptNumber}`);
});

socket.on('reconnect_error', (error) => {
  console.error('Error al reconectar el socket:', error);
});

socket.on('connect_error', (error) => {
  console.error('Error de conexión del socket:', error);
});

// Función para verificar el estado de la conexión
export const isSocketConnected = () => {
  return socket.connected;
};

// Función para reconectar manualmente si es necesario
export const reconnectSocket = () => {
  if (!socket.connected) {
    console.log('Intentando reconexión manual del socket...');
    socket.connect();
  }
};

// El socket se conecta automáticamente con autoConnect: true
console.log('Socket configurado para conectar automáticamente');
