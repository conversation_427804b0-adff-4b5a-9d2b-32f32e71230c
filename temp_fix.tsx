import React, { useState } from 'react';
import Modal from 'react-modal';

// Componente de ejemplo para probar la estructura JSX
const TempFix = () => {
  const [showModal, setShowModal] = useState(false);
  
  return (
    <div>
      <h1>Prueba de estructura JSX</h1>
      <button onClick={() => setShowModal(true)}>Abrir Modal</button>
      
      <Modal
        isOpen={showModal}
        onRequestClose={() => setShowModal(false)}
        contentLabel="Modal de prueba"
      >
        <h2>Modal de prueba</h2>
        <button onClick={() => setShowModal(false)}>Cerrar</button>
      </Modal>
    </div>
  );
};

export default TempFix;
