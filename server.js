import express from 'express';
import cors from 'cors';
import http from 'http';
import { Server } from 'socket.io';
import axios from 'axios';

const app = express();
app.use(cors());
app.use(express.json({ limit: '50mb' }));

const server = http.createServer(app);
const io = new Server(server, {
  cors: {
    origin: '*',
    methods: ['GET', 'POST']
  }
});

// Simulación de mensajes
const messages = {};

// Endpoint para enviar mensajes
app.post('/send-message', (req, res) => {
  const { to, message } = req.body;

  if (!to || !message) {
    return res.status(400).json({ error: 'Se requiere destinatario y mensaje' });
  }

  const timestamp = new Date().toISOString();
  const newMessage = {
    from: '<EMAIL>',
    to: `${to}@c.us`,
    body: message,
    timestamp,
    direction: 'out'
  };

  // Guardar el mensaje en la simulación
  if (!messages[to]) {
    messages[to] = [];
  }
  messages[to].push(newMessage);

  // Emitir el mensaje a través de Socket.IO
  io.emit('new-message', newMessage);

  res.json({ success: true, message: 'Mensaje enviado' });
});

// Endpoint para enviar archivos
app.post('/send-file', (req, res) => {
  const { to, file } = req.body;

  if (!to || !file || !file.data || !file.mimetype) {
    return res.status(400).json({ error: 'Se requiere destinatario y archivo válido' });
  }

  const timestamp = new Date().toISOString();
  const newMessage = {
    from: '<EMAIL>',
    to: `${to}@c.us`,
    body: '',
    timestamp,
    direction: 'out',
    media: {
      data: file.data,
      mimetype: file.mimetype,
      filename: file.filename || 'archivo'
    }
  };

  // Guardar el mensaje en la simulación
  if (!messages[to]) {
    messages[to] = [];
  }
  messages[to].push(newMessage);

  // Emitir el mensaje a través de Socket.IO
  io.emit('new-message', newMessage);

  res.json({ success: true, message: 'Archivo enviado' });
});

// Conexión de Socket.IO
io.on('connection', (socket) => {
  console.log('Cliente conectado');

  // Enviar mensajes existentes al cliente
  Object.keys(messages).forEach(phone => {
    messages[phone].forEach(msg => {
      socket.emit('new-message', msg);
    });
  });

  // Manejar login de usuario
  socket.on('user-login', (data) => {
    console.log(`Usuario ${data.userName} (${data.userId}) inició sesión`);
    socket.userId = data.userId;
    socket.userName = data.userName;

    // Emitir actualización de usuarios conectados
    io.emit('clients-status-update', {
      connectedUsers: getConnectedUsers(),
      selectedChats: getSelectedChats()
    });
  });

  // Manejar logout de usuario
  socket.on('user-logout', (data) => {
    console.log(`Usuario ${data.userId} cerró sesión`);

    // Limpiar información del socket
    delete socket.userId;
    delete socket.userName;

    // Emitir actualización de usuarios conectados
    io.emit('clients-status-update', {
      connectedUsers: getConnectedUsers(),
      selectedChats: getSelectedChats()
    });
  });

  socket.on('disconnect', () => {
    console.log('Cliente desconectado');

    // Emitir actualización de usuarios conectados cuando alguien se desconecta
    io.emit('clients-status-update', {
      connectedUsers: getConnectedUsers(),
      selectedChats: getSelectedChats()
    });
  });
});

// Iniciar el servidor
const PORT = process.env.PORT || 3001;
server.listen(PORT, () => {
  console.log(`Servidor ejecutándose en el puerto ${PORT}`);
});
