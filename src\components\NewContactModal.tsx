import React, { useState } from 'react';
import Modal from 'react-modal';
import axios from 'axios';

interface NewContactModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialPhone?: string;
  userApiValue: string;
}

const NewContactModal: React.FC<NewContactModalProps> = ({ isOpen, onClose, initialPhone = '', userApiValue }) => {
  const [formData, setFormData] = useState({
    nombre: '',
    apellido: '',
    localidad: '',
    direccion: '',
    telefono: initialPhone,
    ciudad: '',
    codigoPostal: '',
    situacionReferencia: '',
    email: '',
    tipo: 'Contacto'
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      console.log('Enviando datos de nuevo contacto al webhook:', formData);
      
      // Enviar los datos al webhook
      const response = await axios.post('https://aawebhook.wifigo.com.mx/webhook/nuevo-contacto', {
        ...formData,
        api: userApiValue // Incluir el valor de la API del usuario
      });
      
      console.log('Respuesta del webhook:', response.data);
      
      // Mostrar mensaje de éxito
      alert('Contacto registrado correctamente');
      
      // Cerrar el modal y reiniciar el formulario
      onClose();
    } catch (error) {
      console.error('Error al enviar datos al webhook:', error);
      alert('Error al registrar el contacto. Por favor, intente nuevamente.');
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onRequestClose={onClose}
      contentLabel="Nuevo Contacto"
      shouldCloseOnOverlayClick={false}
      shouldCloseOnEsc={true}
      ariaHideApp={false}
      style={{
        content: {
          position: 'fixed',
          top: '0',
          right: '0',
          bottom: '0',
          left: 'auto',
          margin: '0',
          width: '28%',
          maxWidth: '33.333%',
          padding: '20px',
          borderRadius: '0',
          overflow: 'auto',
          zIndex: 1000
        },
        overlay: {
          backgroundColor: 'transparent',
          zIndex: 999,
          pointerEvents: 'none' // Permite interactuar con elementos debajo del overlay
        }
      }}
      className={{
        base: 'modal-content',
        afterOpen: 'modal-content-after-open',
        beforeClose: 'modal-content-before-close'
      }}
      overlayClassName={{
        base: 'modal-overlay',
        afterOpen: 'modal-overlay-after-open',
        beforeClose: 'modal-overlay-before-close'
      }}
    >
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-bold">Nuevo Contacto</h2>
        <button
          onClick={onClose}
          className="text-gray-500 hover:text-gray-700"
        >
          ✕
        </button>
      </div>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Campo oculto para el valor de la API del usuario */}
        <input
          type="hidden"
          name="api"
          value={userApiValue}
        />
        
        {/* Nombre */}
        <div>
          <label className="block font-semibold">Nombre</label>
          <input
            type="text"
            name="nombre"
            value={formData.nombre}
            onChange={handleChange}
            className="border rounded w-full p-2"
            required
          />
        </div>
        
        {/* Apellido */}
        <div>
          <label className="block font-semibold">Apellido</label>
          <input
            type="text"
            name="apellido"
            value={formData.apellido}
            onChange={handleChange}
            className="border rounded w-full p-2"
            required
          />
        </div>
        
        {/* Localidad */}
        <div>
          <label className="block font-semibold">Localidad</label>
          <input
            type="text"
            name="localidad"
            value={formData.localidad}
            onChange={handleChange}
            className="border rounded w-full p-2"
          />
        </div>
        
        {/* Dirección */}
        <div>
          <label className="block font-semibold">Dirección</label>
          <input
            type="text"
            name="direccion"
            value={formData.direccion}
            onChange={handleChange}
            className="border rounded w-full p-2"
          />
        </div>
        
        {/* Teléfono */}
        <div>
          <label className="block font-semibold">Teléfono</label>
          <input
            type="text"
            name="telefono"
            value={formData.telefono}
            onChange={handleChange}
            className="border rounded w-full p-2"
            required
          />
        </div>
        
        {/* Ciudad/Municipio */}
        <div>
          <label className="block font-semibold">Ciudad/Municipio</label>
          <input
            type="text"
            name="ciudad"
            value={formData.ciudad}
            onChange={handleChange}
            className="border rounded w-full p-2"
          />
        </div>
        
        {/* Código Postal */}
        <div>
          <label className="block font-semibold">Código Postal</label>
          <input
            type="text"
            name="codigoPostal"
            value={formData.codigoPostal}
            onChange={handleChange}
            className="border rounded w-full p-2"
          />
        </div>
        
        {/* Situación Referencia */}
        <div>
          <label className="block font-semibold">Situación Referencia</label>
          <textarea
            name="situacionReferencia"
            value={formData.situacionReferencia}
            onChange={handleChange}
            className="border rounded w-full p-2"
            rows={3}
          />
        </div>
        
        {/* Email */}
        <div>
          <label className="block font-semibold">Email</label>
          <input
            type="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
            className="border rounded w-full p-2"
          />
        </div>
        
        {/* Tipo */}
        <div>
          <label className="block font-semibold">Tipo</label>
          <select
            name="tipo"
            value={formData.tipo}
            onChange={handleChange}
            className="border rounded w-full p-2"
          >
            <option value="Contacto">Contacto</option>
            <option value="Cámaras">Cámaras</option>
            <option value="Torres">Torres</option>
            <option value="Paneles">Paneles</option>
            <option value="Radios">Radios</option>
          </select>
        </div>
        
        {/* Botones de acción */}
        <div className="flex justify-end space-x-3 pt-4">
          <button
            type="button"
            onClick={onClose}
            className="bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded"
          >
            Cancelar
          </button>
          <button
            type="submit"
            className="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded"
          >
            Guardar contacto
          </button>
        </div>
      </form>
    </Modal>
  );
};

export default NewContactModal;
