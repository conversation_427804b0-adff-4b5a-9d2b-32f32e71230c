import { api } from '../config/api';

export const whatsappService = {
  getClientsStatus: () => api.get('/clients-status'),
  connectClient: (clientId: string) => api.post('/connect-client', { clientId }),
  disconnectClient: (clientId: string) => api.post('/disconnect-client', { clientId }),
  sendMessage: (to: string, message: string, clientId: string) => 
    api.post('/send-message', { to, message, clientId }),
  sendFile: (formData: FormData) => 
    api.post('/send-file', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
};