/* Estilos personalizados para la barra de desplazamiento */

/* Contenedor principal con espacio reservado para la barra de desplazamiento */
.chat-messages-container {
  scrollbar-gutter: stable;
  scrollbar-width: thin; /* Para Firefox */
  scrollbar-color: rgba(147, 197, 253, 0.3) rgba(243, 244, 246, 0.2); /* Thumb y track para Firefox */
}

/* Estilos para la barra de desplazamiento en WebKit (Chrome, Safari, Edge) */
.chat-messages-container::-webkit-scrollbar {
  width: 8px; /* Más delgada y tenue */
}

.chat-messages-container::-webkit-scrollbar-track {
  background: rgba(243, 244, 246, 0.2); /* Gris muy claro y transparente */
  border-radius: 12px;
}

.chat-messages-container::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(147, 197, 253, 0.3), rgba(196, 181, 253, 0.3)); /* Degradado azul-púrpura tenue */
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.2s ease;
}

.chat-messages-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(147, 197, 253, 0.5), rgba(196, 181, 253, 0.5)); /* Más visible en hover */
  border: 1px solid rgba(255, 255, 255, 0.5);
}

.chat-messages-container::-webkit-scrollbar-thumb:active {
  background: linear-gradient(135deg, rgba(147, 197, 253, 0.7), rgba(196, 181, 253, 0.7)); /* Más visible al arrastrar */
}

/* Eliminar las flechas para un diseño más moderno y tenue */
.chat-messages-container::-webkit-scrollbar-button {
  display: none;
}

/* Estilos para la barra de scroll de la lista de conversaciones */
.conversations-list {
  scrollbar-width: thin; /* Para Firefox */
  scrollbar-color: rgba(147, 197, 253, 0.3) rgba(243, 244, 246, 0.2); /* Thumb y track para Firefox */
}

.conversations-list::-webkit-scrollbar {
  width: 6px; /* Aún más delgada para la lista lateral */
}

.conversations-list::-webkit-scrollbar-track {
  background: rgba(243, 244, 246, 0.1); /* Casi invisible */
  border-radius: 10px;
}

.conversations-list::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(147, 197, 253, 0.2), rgba(196, 181, 253, 0.2)); /* Muy tenue */
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.2s ease;
}

.conversations-list::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(147, 197, 253, 0.4), rgba(196, 181, 253, 0.4)); /* Más visible en hover */
  border: 1px solid rgba(255, 255, 255, 0.4);
}

.conversations-list::-webkit-scrollbar-thumb:active {
  background: linear-gradient(135deg, rgba(147, 197, 253, 0.6), rgba(196, 181, 253, 0.6)); /* Más visible al arrastrar */
}

.conversations-list::-webkit-scrollbar-button {
  display: none;
}

/* Estilos globales para todas las barras de scroll del tema */
* {
  scrollbar-width: thin; /* Para Firefox */
  scrollbar-color: rgba(147, 197, 253, 0.2) rgba(243, 244, 246, 0.1); /* Thumb y track para Firefox */
}

*::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

*::-webkit-scrollbar-track {
  background: rgba(243, 244, 246, 0.1);
  border-radius: 10px;
}

*::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(147, 197, 253, 0.2), rgba(196, 181, 253, 0.2));
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.2s ease;
}

*::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(147, 197, 253, 0.4), rgba(196, 181, 253, 0.4));
  border: 1px solid rgba(255, 255, 255, 0.3);
}

*::-webkit-scrollbar-thumb:active {
  background: linear-gradient(135deg, rgba(147, 197, 253, 0.6), rgba(196, 181, 253, 0.6));
}

*::-webkit-scrollbar-button {
  display: none;
}

*::-webkit-scrollbar-corner {
  background: transparent;
}

/* Estilo para cuando se arrastra un archivo sobre el área de chat */
.chat-messages-container.drag-over {
  border: 2px dashed #4CAF50;
  background-color: rgba(76, 175, 80, 0.1);
  position: relative;
}

.chat-messages-container.drag-over::after {
  content: "Suelta para enviar";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(76, 175, 80, 0.9);
  color: white;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: bold;
  pointer-events: none;
}
