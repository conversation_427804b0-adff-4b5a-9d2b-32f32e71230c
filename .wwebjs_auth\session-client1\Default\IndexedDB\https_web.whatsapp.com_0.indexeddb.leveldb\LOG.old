2025/05/02-12:22:26.989 1e24 Reusing MANIFEST C:\Users\<USER>\Desktop\pagina-wifigo - copia\.wwebjs_auth\session-client1\Default\IndexedDB\https_web.whatsapp.com_0.indexeddb.leveldb/MANIFEST-000001
2025/05/02-12:22:26.989 1e24 Recovering log #78
2025/05/02-12:22:27.005 1e24 Reusing old log C:\Users\<USER>\Desktop\pagina-wifigo - copia\.wwebjs_auth\session-client1\Default\IndexedDB\https_web.whatsapp.com_0.indexeddb.leveldb/000078.log 
2025/05/02-12:22:27.005 1e24 Delete type=2 #77
2025/05/02-12:22:27.345 1e24 Manual compaction at level-0 from '\x00#\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00$\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/02-12:22:27.349 310c Manual compaction at level-0 from '\x00!\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00"\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/02-12:22:27.352 310c Level-0 table #83: started
2025/05/02-12:22:27.357 310c Level-0 table #83: 84198 bytes OK
2025/05/02-12:22:27.358 310c Delete type=0 #78
2025/05/02-12:22:27.359 310c Manual compaction at level-0 from '\x00$\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00%\x00\x00\x00' @ 0 : 0; will stop at '\x00%\x00\x00\xc8\x0d\x00l\x00o\x00c\x00a\x00l\x00_\x00s\x00t\x00o\x00r\x00a\x00g\x00e' @ 21331 : 1
2025/05/02-12:22:27.359 310c Compacting 1@0 + 1@1 files
2025/05/02-12:22:27.368 310c Generated table #84@0: 2826 keys, 40959 bytes
2025/05/02-12:22:27.368 310c Compacted 1@0 + 1@1 files => 40959 bytes
2025/05/02-12:22:27.369 310c compacted to: files[ 0 1 1 0 0 0 0 ]
2025/05/02-12:22:27.369 310c Delete type=2 #83
2025/05/02-12:22:27.370 310c Manual compaction at level-0 from '\x00%\x00\x00\xc8\x0d\x00l\x00o\x00c\x00a\x00l\x00_\x00s\x00t\x00o\x00r\x00a\x00g\x00e' @ 21331 : 1 .. '\x00%\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/02-12:22:27.372 1e24 Level-0 table #86: started
2025/05/02-12:22:27.382 1e24 Level-0 table #86: 756 bytes OK
2025/05/02-12:22:27.384 1e24 Delete type=2 #80
2025/05/02-12:22:27.384 1e24 Delete type=0 #82
2025/05/02-12:22:27.384 1e24 Manual compaction at level-0 from '\x00 \x00\x00\x00' @ 72057594037927935 : 1 .. '\x00!\x00\x00\x00' @ 0 : 0; will stop at '\x00&\x00\x00\x05' @ 23639 : 1
2025/05/02-12:22:27.384 1e24 Compacting 1@0 + 1@1 files
2025/05/02-12:22:27.389 1e24 Generated table #87@0: 2806 keys, 40398 bytes
2025/05/02-12:22:27.389 1e24 Compacted 1@0 + 1@1 files => 40398 bytes
2025/05/02-12:22:27.390 1e24 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/05/02-12:22:27.390 1e24 Delete type=2 #86
2025/05/02-12:22:27.391 1e24 Manual compaction at level-0 from '\x00&\x00\x00\x05' @ 23639 : 1 .. '\x00!\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/02-12:22:27.392 3a20 Level-0 table #89: started
2025/05/02-12:22:27.404 3a20 Level-0 table #89: 500 bytes OK
2025/05/02-12:22:27.405 3a20 Delete type=2 #84
2025/05/02-12:22:27.405 3a20 Delete type=0 #85
2025/05/02-12:22:27.406 3a20 Manual compaction at level-0 from '\x00%\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00&\x00\x00\x00' @ 0 : 0; will stop at '\x00%\x00\x00\xc8\x0d\x00l\x00o\x00c\x00a\x00l\x00_\x00s\x00t\x00o\x00r\x00a\x00g\x00e' @ 23691 : 0
2025/05/02-12:22:27.406 3a20 Compacting 1@0 + 1@1 files
2025/05/02-12:22:27.411 3a20 Generated table #90@0: 2785 keys, 40040 bytes
2025/05/02-12:22:27.411 3a20 Compacted 1@0 + 1@1 files => 40040 bytes
2025/05/02-12:22:27.411 3a20 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/05/02-12:22:27.412 3a20 Delete type=2 #89
2025/05/02-12:22:27.412 3a20 Manual compaction at level-0 from '\x00%\x00\x00\xc8\x0d\x00l\x00o\x00c\x00a\x00l\x00_\x00s\x00t\x00o\x00r\x00a\x00g\x00e' @ 23691 : 0 .. '\x00&\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/02-12:22:27.414 487c Level-0 table #92: started
2025/05/02-12:22:27.417 487c Level-0 table #92: 10331 bytes OK
2025/05/02-12:22:27.418 487c Delete type=2 #87
2025/05/02-12:22:27.418 487c Delete type=0 #88
2025/05/02-12:22:27.419 487c Manual compaction at level-0 from '\x00\x14\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x15\x00\x00\x00' @ 0 : 0; will stop at '\x00\x14\x0a\x02\x03\x00\x00\x00\x00\x00\x00\x00@' @ 24294 : 0
2025/05/02-12:22:27.419 487c Compacting 1@0 + 1@1 files
2025/05/02-12:22:27.423 487c Generated table #93@0: 2461 keys, 20984 bytes
2025/05/02-12:22:27.423 487c Compacted 1@0 + 1@1 files => 20984 bytes
2025/05/02-12:22:27.423 487c compacted to: files[ 0 1 1 0 0 0 0 ]
2025/05/02-12:22:27.424 487c Delete type=2 #92
2025/05/02-12:22:27.424 487c Manual compaction at level-0 from '\x00\x14\x0a\x02\x03\x00\x00\x00\x00\x00\x00\x00@' @ 24294 : 0 .. '\x00\x15\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/02-12:22:27.424 487c Manual compaction at level-1 from '\x00\x14\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x15\x00\x00\x00' @ 0 : 0; will stop at '\x00&\x00\x00\x05' @ 23639 : 1
2025/05/02-12:22:27.424 487c Compacting 1@1 + 1@2 files
2025/05/02-12:22:27.434 487c Generated table #94@1: 12 keys, 453 bytes
2025/05/02-12:22:27.434 487c Compacted 1@1 + 1@2 files => 453 bytes
2025/05/02-12:22:27.434 487c compacted to: files[ 0 0 1 0 0 0 0 ]
2025/05/02-12:22:27.434 487c Delete type=2 #93
2025/05/02-12:22:27.434 487c Manual compaction at level-1 from '\x00&\x00\x00\x05' @ 23639 : 1 .. '\x00\x15\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/02-12:22:28.570 1e24 Manual compaction at level-0 from '\x00'\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00(\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/02-12:22:28.570 1e24 Level-0 table #96: started
2025/05/02-12:22:28.573 1e24 Level-0 table #96: 19136 bytes OK
2025/05/02-12:22:28.574 1e24 Delete type=2 #48
2025/05/02-12:22:28.574 1e24 Delete type=2 #90
2025/05/02-12:22:28.574 1e24 Delete type=0 #91
2025/05/02-12:22:28.575 1e24 Manual compaction at level-0 from '\x00(\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00)\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/02-12:22:28.575 1e24 Level-0 table #98: started
2025/05/02-12:22:28.584 1e24 Level-0 table #98: 753 bytes OK
2025/05/02-12:22:28.584 1e24 Delete type=0 #95
2025/05/02-12:22:28.585 1e24 Manual compaction at level-0 from '\x00)\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00*\x00\x00\x00' @ 0 : 0; will stop at '\x00)\x00\x00\x05' @ 25497 : 0
2025/05/02-12:22:28.585 1e24 Compacting 1@0 + 1@1 files
2025/05/02-12:22:28.587 1e24 Generated table #99@0: 716 keys, 11798 bytes
2025/05/02-12:22:28.587 1e24 Compacted 1@0 + 1@1 files => 11798 bytes
2025/05/02-12:22:28.588 1e24 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/05/02-12:22:28.588 1e24 Delete type=2 #98
2025/05/02-12:22:28.589 1e24 Manual compaction at level-0 from '\x00)\x00\x00\x05' @ 25497 : 0 .. '\x00*\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/02-12:22:28.590 3a20 Level-0 table #101: started
2025/05/02-12:22:28.604 3a20 Level-0 table #101: 1131 bytes OK
2025/05/02-12:22:28.604 3a20 Delete type=2 #96
2025/05/02-12:22:28.604 3a20 Delete type=0 #97
2025/05/02-12:22:28.605 3a20 Manual compaction at level-0 from '\x00*\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00+\x00\x00\x00' @ 0 : 0; will stop at '\x002\x00\x00\x05' @ 25549 : 1
2025/05/02-12:22:28.605 3a20 Compacting 1@0 + 1@1 files
2025/05/02-12:22:28.608 3a20 Generated table #102@0: 740 keys, 12380 bytes
2025/05/02-12:22:28.608 3a20 Compacted 1@0 + 1@1 files => 12380 bytes
2025/05/02-12:22:28.608 3a20 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/05/02-12:22:28.609 3a20 Delete type=2 #101
2025/05/02-12:22:28.609 3a20 Manual compaction at level-0 from '\x002\x00\x00\x05' @ 25549 : 1 .. '\x00+\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/02-12:22:28.609 310c Level-0 table #104: started
2025/05/02-12:22:28.619 310c Level-0 table #104: 3913 bytes OK
2025/05/02-12:22:28.620 310c Delete type=2 #99
2025/05/02-12:22:28.620 310c Delete type=0 #100
2025/05/02-12:22:28.620 310c Manual compaction at level-0 from '\x00+\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00,\x00\x00\x00' @ 0 : 0; will stop at '\x003\x00\x00\xc8\x13\x00s\x00i\x00g\x00n\x00e\x00d\x00-\x00p\x00r\x00e\x00k\x00e\x00y\x00-\x00s\x00t\x00o\x00r\x00e' @ 25716 : 1
2025/05/02-12:22:28.620 310c Compacting 1@0 + 1@1 files
2025/05/02-12:22:28.623 310c Generated table #105@0: 949 keys, 15715 bytes
2025/05/02-12:22:28.623 310c Compacted 1@0 + 1@1 files => 15715 bytes
2025/05/02-12:22:28.624 310c compacted to: files[ 0 1 1 0 0 0 0 ]
2025/05/02-12:22:28.624 310c Delete type=2 #104
2025/05/02-12:22:28.624 310c Manual compaction at level-0 from '\x003\x00\x00\xc8\x13\x00s\x00i\x00g\x00n\x00e\x00d\x00-\x00p\x00r\x00e\x00k\x00e\x00y\x00-\x00s\x00t\x00o\x00r\x00e' @ 25716 : 1 .. '\x00,\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/02-12:22:28.625 487c Level-0 table #107: started
2025/05/02-12:22:28.634 487c Level-0 table #107: 527 bytes OK
2025/05/02-12:22:28.635 487c Delete type=2 #102
2025/05/02-12:22:28.635 487c Delete type=0 #103
2025/05/02-12:22:28.635 487c Manual compaction at level-0 from '\x00,\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00-\x00\x00\x00' @ 0 : 0; will stop at '\x004\x00\x00\x05' @ 25797 : 1
2025/05/02-12:22:28.635 487c Compacting 1@0 + 1@1 files
2025/05/02-12:22:28.639 487c Generated table #108@0: 953 keys, 15750 bytes
2025/05/02-12:22:28.639 487c Compacted 1@0 + 1@1 files => 15750 bytes
2025/05/02-12:22:28.639 487c compacted to: files[ 0 1 1 0 0 0 0 ]
2025/05/02-12:22:28.639 487c Delete type=2 #107
2025/05/02-12:22:28.640 487c Manual compaction at level-0 from '\x004\x00\x00\x05' @ 25797 : 1 .. '\x00-\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/02-12:22:28.640 487c Level-0 table #110: started
2025/05/02-12:22:28.647 487c Level-0 table #110: 249 bytes OK
2025/05/02-12:22:28.648 487c Delete type=2 #105
2025/05/02-12:22:28.648 487c Delete type=0 #106
2025/05/02-12:22:28.648 487c Manual compaction at level-0 from '\x00-\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00.\x00\x00\x00' @ 0 : 0; will stop at '\x00-\x00\x00\x05' @ 25817 : 0
2025/05/02-12:22:28.648 487c Compacting 1@0 + 1@1 files
2025/05/02-12:22:28.651 487c Generated table #111@0: 951 keys, 15679 bytes
2025/05/02-12:22:28.651 487c Compacted 1@0 + 1@1 files => 15679 bytes
2025/05/02-12:22:28.652 487c compacted to: files[ 0 1 1 0 0 0 0 ]
2025/05/02-12:22:28.652 487c Delete type=2 #110
2025/05/02-12:22:28.652 487c Manual compaction at level-0 from '\x00-\x00\x00\x05' @ 25817 : 0 .. '\x00.\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/02-12:22:28.653 487c Level-0 table #113: started
2025/05/02-12:22:28.660 487c Level-0 table #113: 249 bytes OK
2025/05/02-12:22:28.661 487c Delete type=2 #108
2025/05/02-12:22:28.661 487c Delete type=0 #109
2025/05/02-12:22:28.661 487c Manual compaction at level-0 from '\x001\x00\x00\x00' @ 72057594037927935 : 1 .. '\x002\x00\x00\x00' @ 0 : 0; will stop at '\x001\x00\x00\x05' @ 25823 : 0
2025/05/02-12:22:28.661 487c Compacting 1@0 + 1@1 files
2025/05/02-12:22:28.664 487c Generated table #114@0: 949 keys, 15666 bytes
2025/05/02-12:22:28.664 487c Compacted 1@0 + 1@1 files => 15666 bytes
2025/05/02-12:22:28.665 487c compacted to: files[ 0 1 1 0 0 0 0 ]
2025/05/02-12:22:28.665 487c Delete type=2 #113
2025/05/02-12:22:28.665 487c Manual compaction at level-0 from '\x001\x00\x00\x05' @ 25823 : 0 .. '\x002\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/02-12:22:28.666 487c Level-0 table #116: started
2025/05/02-12:22:28.673 487c Level-0 table #116: 249 bytes OK
2025/05/02-12:22:28.674 487c Delete type=2 #111
2025/05/02-12:22:28.674 487c Delete type=0 #112
2025/05/02-12:22:28.674 487c Manual compaction at level-0 from '\x00/\x00\x00\x00' @ 72057594037927935 : 1 .. '\x000\x00\x00\x00' @ 0 : 0; will stop at '\x00/\x00\x00\x05' @ 25829 : 0
2025/05/02-12:22:28.674 487c Compacting 1@0 + 1@1 files
2025/05/02-12:22:28.677 487c Generated table #117@0: 947 keys, 15536 bytes
2025/05/02-12:22:28.677 487c Compacted 1@0 + 1@1 files => 15536 bytes
2025/05/02-12:22:28.677 487c compacted to: files[ 0 1 1 0 0 0 0 ]
2025/05/02-12:22:28.677 487c Delete type=2 #116
2025/05/02-12:22:28.678 487c Manual compaction at level-0 from '\x00/\x00\x00\x05' @ 25829 : 0 .. '\x000\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/02-12:22:28.678 487c Level-0 table #119: started
2025/05/02-12:22:28.685 487c Level-0 table #119: 249 bytes OK
2025/05/02-12:22:28.686 487c Delete type=2 #114
2025/05/02-12:22:28.686 487c Delete type=0 #115
2025/05/02-12:22:28.686 487c Manual compaction at level-0 from '\x000\x00\x00\x00' @ 72057594037927935 : 1 .. '\x001\x00\x00\x00' @ 0 : 0; will stop at '\x000\x00\x00\x05' @ 25835 : 0
2025/05/02-12:22:28.686 487c Compacting 1@0 + 1@1 files
2025/05/02-12:22:28.689 487c Generated table #120@0: 945 keys, 15564 bytes
2025/05/02-12:22:28.689 487c Compacted 1@0 + 1@1 files => 15564 bytes
2025/05/02-12:22:28.689 487c compacted to: files[ 0 1 1 0 0 0 0 ]
2025/05/02-12:22:28.690 487c Delete type=2 #119
2025/05/02-12:22:28.690 487c Manual compaction at level-0 from '\x000\x00\x00\x05' @ 25835 : 0 .. '\x001\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/02-12:22:28.690 487c Level-0 table #122: started
2025/05/02-12:22:28.697 487c Level-0 table #122: 249 bytes OK
2025/05/02-12:22:28.698 487c Delete type=2 #117
2025/05/02-12:22:28.698 487c Delete type=0 #118
2025/05/02-12:22:28.698 487c Manual compaction at level-0 from '\x00.\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00/\x00\x00\x00' @ 0 : 0; will stop at '\x00.\x00\x00\x05' @ 25841 : 0
2025/05/02-12:22:28.698 487c Compacting 1@0 + 1@1 files
2025/05/02-12:22:28.700 487c Generated table #123@0: 943 keys, 15414 bytes
2025/05/02-12:22:28.700 487c Compacted 1@0 + 1@1 files => 15414 bytes
2025/05/02-12:22:28.701 487c compacted to: files[ 0 1 1 0 0 0 0 ]
2025/05/02-12:22:28.701 487c Delete type=2 #122
2025/05/02-12:22:28.701 487c Manual compaction at level-0 from '\x00.\x00\x00\x05' @ 25841 : 0 .. '\x00/\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/02-12:22:28.702 487c Level-0 table #125: started
2025/05/02-12:22:28.709 487c Level-0 table #125: 1202 bytes OK
2025/05/02-12:22:28.710 487c Delete type=2 #120
2025/05/02-12:22:28.710 487c Delete type=0 #121
2025/05/02-12:22:28.710 487c Manual compaction at level-0 from '\x004\x00\x00\x00' @ 72057594037927935 : 1 .. '\x005\x00\x00\x00' @ 0 : 0; will stop at '\x004\x00\x00\x05' @ 25961 : 0
2025/05/02-12:22:28.710 487c Compacting 1@0 + 1@1 files
2025/05/02-12:22:28.713 487c Generated table #126@0: 941 keys, 14813 bytes
2025/05/02-12:22:28.713 487c Compacted 1@0 + 1@1 files => 14813 bytes
2025/05/02-12:22:28.713 487c compacted to: files[ 0 1 1 0 0 0 0 ]
2025/05/02-12:22:28.713 487c Delete type=2 #125
2025/05/02-12:22:28.713 487c Manual compaction at level-0 from '\x004\x00\x00\x05' @ 25961 : 0 .. '\x005\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/02-14:07:50.543 2b34 Compacting 1@1 + 1@2 files
2025/05/02-14:07:50.551 2b34 Generated table #127@1: 410 keys, 10049 bytes
2025/05/02-14:07:50.551 2b34 Compacted 1@1 + 1@2 files => 10049 bytes
2025/05/02-14:07:50.552 2b34 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/05/02-14:07:50.552 2b34 Delete type=2 #94
2025/05/02-14:07:50.552 2b34 Delete type=2 #123
2025/05/02-14:07:50.552 2b34 Delete type=2 #126
2025/05/02-14:21:53.077 1710 Level-0 table #129: started
2025/05/02-14:21:53.094 1710 Level-0 table #129: 1184652 bytes OK
2025/05/02-14:21:53.095 1710 Delete type=0 #124
2025/05/02-14:21:53.339 378c Compacting 1@1 + 1@2 files
2025/05/02-14:21:53.357 378c Generated table #130@1: 8324 keys, 468326 bytes
2025/05/02-14:21:53.357 378c Compacted 1@1 + 1@2 files => 468326 bytes
2025/05/02-14:21:53.357 378c compacted to: files[ 0 0 1 0 0 0 0 ]
2025/05/02-14:21:53.358 378c Delete type=2 #127
2025/05/02-14:21:53.358 378c Delete type=2 #129
2025/05/02-15:35:08.754 3578 Level-0 table #132: started
2025/05/02-15:35:08.773 3578 Level-0 table #132: 1015043 bytes OK
2025/05/02-15:35:08.774 3578 Delete type=0 #128
2025/05/02-15:36:17.901 3380 Compacting 1@1 + 1@2 files
2025/05/02-15:36:17.923 3380 Generated table #133@1: 13892 keys, 883285 bytes
2025/05/02-15:36:17.923 3380 Compacted 1@1 + 1@2 files => 883285 bytes
2025/05/02-15:36:17.924 3380 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/05/02-15:36:17.924 3380 Delete type=2 #130
2025/05/02-15:36:17.924 3380 Delete type=2 #132
