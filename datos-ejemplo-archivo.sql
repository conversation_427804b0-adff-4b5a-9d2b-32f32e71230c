-- Script para insertar datos de ejemplo en el sistema de archivos financieros
-- Ejecuta este SQL en tu panel de Supabase para crear la estructura de carpetas de ejemplo

-- IMPORTANTE: Reemplaza 'tu-user-id-aqui' con tu UUID real de usuario
-- Puedes obtener tu user ID ejecutando: SELECT auth.uid();

-- 1. Carpeta raíz FINANZAS
INSERT INTO public.folders (name, parent_id, created_by) VALUES 
('FINANZAS', NULL, 'tu-user-id-aqui');

-- 2. Subcarpeta FINANZAS (dentro de FINANZAS)
INSERT INTO public.folders (name, parent_id, created_by) 
SELECT 'FINANZAS', f.id, 'tu-user-id-aqui'
FROM public.folders f 
WHERE f.name = 'FINANZAS' AND f.parent_id IS NULL;

-- 3. Documentos Finanzas
INSERT INTO public.folders (name, parent_id, created_by) 
SELECT 'Documentos Finanzas', f.id, 'tu-user-id-aqui'
FROM public.folders f 
WHERE f.name = 'FINANZAS' AND f.parent_id IS NOT NULL;

-- 4. ADMON Y CONTABILIDAD
INSERT INTO public.folders (name, parent_id, created_by) 
SELECT 'ADMON Y CONTABILIDAD', f.id, 'tu-user-id-aqui'
FROM public.folders f 
WHERE f.name = 'Documentos Finanzas';

-- 5. Carpetas por año
INSERT INTO public.folders (name, parent_id, created_by) 
SELECT '2025', f.id, 'tu-user-id-aqui'
FROM public.folders f 
WHERE f.name = 'ADMON Y CONTABILIDAD';

INSERT INTO public.folders (name, parent_id, created_by) 
SELECT '2024', f.id, 'tu-user-id-aqui'
FROM public.folders f 
WHERE f.name = 'ADMON Y CONTABILIDAD';

-- 6. Carpetas por banco (dentro de 2025)
INSERT INTO public.folders (name, parent_id, created_by) 
SELECT 'BBVA', f.id, 'tu-user-id-aqui'
FROM public.folders f 
WHERE f.name = '2025';

INSERT INTO public.folders (name, parent_id, created_by) 
SELECT 'SANTANDER', f.id, 'tu-user-id-aqui'
FROM public.folders f 
WHERE f.name = '2025';

INSERT INTO public.folders (name, parent_id, created_by) 
SELECT 'BANAMEX', f.id, 'tu-user-id-aqui'
FROM public.folders f 
WHERE f.name = '2025';

-- 7. Carpetas por mes (dentro de BBVA)
INSERT INTO public.folders (name, parent_id, created_by) 
SELECT 'ENERO', f.id, 'tu-user-id-aqui'
FROM public.folders f 
WHERE f.name = 'BBVA';

INSERT INTO public.folders (name, parent_id, created_by) 
SELECT 'FEBRERO', f.id, 'tu-user-id-aqui'
FROM public.folders f 
WHERE f.name = 'BBVA';

INSERT INTO public.folders (name, parent_id, created_by) 
SELECT 'MARZO', f.id, 'tu-user-id-aqui'
FROM public.folders f 
WHERE f.name = 'BBVA';

INSERT INTO public.folders (name, parent_id, created_by) 
SELECT 'ABRIL', f.id, 'tu-user-id-aqui'
FROM public.folders f 
WHERE f.name = 'BBVA';

INSERT INTO public.folders (name, parent_id, created_by) 
SELECT 'MAYO', f.id, 'tu-user-id-aqui'
FROM public.folders f 
WHERE f.name = 'BBVA';

-- 8. Carpetas de movimientos (dentro de MAYO)
INSERT INTO public.folders (name, parent_id, created_by) 
SELECT '01.-PAGO NOMINA', f.id, 'tu-user-id-aqui'
FROM public.folders f 
WHERE f.name = 'MAYO';

INSERT INTO public.folders (name, parent_id, created_by) 
SELECT '02.-PAGO PROVEEDORES', f.id, 'tu-user-id-aqui'
FROM public.folders f 
WHERE f.name = 'MAYO';

INSERT INTO public.folders (name, parent_id, created_by) 
SELECT '03.-TRANSFERENCIA BANCARIA', f.id, 'tu-user-id-aqui'
FROM public.folders f 
WHERE f.name = 'MAYO';

INSERT INTO public.folders (name, parent_id, created_by) 
SELECT '04.-DEPOSITO CLIENTE', f.id, 'tu-user-id-aqui'
FROM public.folders f 
WHERE f.name = 'MAYO';

INSERT INTO public.folders (name, parent_id, created_by) 
SELECT '05.-COMISIONES BANCARIAS', f.id, 'tu-user-id-aqui'
FROM public.folders f 
WHERE f.name = 'MAYO';

-- 9. Archivos de ejemplo (dentro de PAGO NOMINA)
-- Nota: Estos son archivos de ejemplo, en la realidad se subirían a través de los webhooks
INSERT INTO public.files (folder_id, created_by, filename, mime_type, size_bytes, storage_path) 
SELECT f.id, 'tu-user-id-aqui', 'comprobante-nomina-mayo-2025.pdf', 'application/pdf', 245760, 'files/comprobante-nomina-mayo-2025.pdf'
FROM public.folders f 
WHERE f.name = '01.-PAGO NOMINA';

INSERT INTO public.files (folder_id, created_by, filename, mime_type, size_bytes, storage_path) 
SELECT f.id, 'tu-user-id-aqui', 'cfdi-nomina-mayo.xml', 'application/xml', 15360, 'files/cfdi-nomina-mayo.xml'
FROM public.folders f 
WHERE f.name = '01.-PAGO NOMINA';

INSERT INTO public.files (folder_id, created_by, filename, mime_type, size_bytes, storage_path) 
SELECT f.id, 'tu-user-id-aqui', 'estado-cuenta-bbva-mayo.jpg', 'image/jpeg', 1024000, 'files/estado-cuenta-bbva-mayo.jpg'
FROM public.folders f 
WHERE f.name = '01.-PAGO NOMINA';

-- 10. Archivos de ejemplo (dentro de PAGO PROVEEDORES)
INSERT INTO public.files (folder_id, created_by, filename, mime_type, size_bytes, storage_path) 
SELECT f.id, 'tu-user-id-aqui', 'factura-proveedor-001.pdf', 'application/pdf', 512000, 'files/factura-proveedor-001.pdf'
FROM public.folders f 
WHERE f.name = '02.-PAGO PROVEEDORES';

INSERT INTO public.files (folder_id, created_by, filename, mime_type, size_bytes, storage_path) 
SELECT f.id, 'tu-user-id-aqui', 'comprobante-transferencia.png', 'image/png', 768000, 'files/comprobante-transferencia.png'
FROM public.folders f 
WHERE f.name = '02.-PAGO PROVEEDORES';

-- 11. Verificar la estructura creada
-- Ejecuta esta consulta para ver la estructura de carpetas:
/*
WITH RECURSIVE folder_tree AS (
  SELECT id, name, parent_id, 0 as level, name as path
  FROM public.folders 
  WHERE parent_id IS NULL
  
  UNION ALL
  
  SELECT f.id, f.name, f.parent_id, ft.level + 1, ft.path || '/' || f.name
  FROM public.folders f
  JOIN folder_tree ft ON f.parent_id = ft.id
)
SELECT 
  REPEAT('  ', level) || name as estructura,
  path,
  level
FROM folder_tree 
ORDER BY path;
*/

-- 12. Ver archivos por carpeta
/*
SELECT 
  f.name as carpeta,
  fi.filename,
  fi.mime_type,
  fi.size_bytes,
  fi.created_at
FROM public.folders f
LEFT JOIN public.files fi ON f.id = fi.folder_id
WHERE fi.id IS NOT NULL
ORDER BY f.name, fi.filename;
*/
