{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "scripts": {"dev": "concurrently --names \"FRONTEN<PERSON>,BACKEND\" --prefix-colors \"blue,green\" \"vite\" \"node server/index.js\"", "frontend": "vite", "backend": "node server/index.js", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@supabase/supabase-js": "^2.49.8", "@tanstack/react-table": "^8.13.2", "airtable": "^0.12.2", "axios": "^1.8.4", "cors": "^2.8.5", "emoji-picker-react": "^4.12.2", "express": "^4.21.2", "lucide-react": "^0.344.0", "qrcode": "^1.5.4", "qrcode-terminal": "^0.12.0", "qrcode.react": "^4.2.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-modal": "^3.16.1", "react-router-dom": "^6.22.3", "recharts": "^2.12.2", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "webdav": "^5.8.0", "whatsapp-web.js": "^1.27.0"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/react-modal": "^3.16.3", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "concurrently": "^9.1.2", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}