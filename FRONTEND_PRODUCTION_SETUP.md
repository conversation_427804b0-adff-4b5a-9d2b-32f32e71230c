# Configuración del Frontend para Producción

## 🚀 Despliegue en central.wifigo.com.mx

### ✅ Configuración Corregida

El frontend ya está configurado correctamente para producción con las siguientes URLs:

```env
# .env.production
VITE_API_BASE_URL=https://ccbak.wifigo.com.mx
VITE_SOCKET_URL=https://ccbak.wifigo.com.mx
VITE_ENV=production
```

## 📋 Instrucciones de Despliegue

### 1. Build para Producción
```bash
# Instalar dependencias
npm install

# Crear build de producción
npm run build
```

### 2. Archivos Generados
El comando `npm run build` genera:
- **<PERSON><PERSON>a `dist/`**: Contiene todos los archivos estáticos
- **Archivos optimizados**: HTML, CSS, JS minificados
- **Assets**: Imágenes, fuentes, etc.

### 3. Desplegar en Servidor Web
```bash
# Copiar contenido de la carpeta dist/ al servidor web
# Ejemplo con rsync:
rsync -avz dist/ usuario@servidor:/var/www/central.wifigo.com.mx/

# O comprimir y subir:
tar -czf frontend-build.tar.gz dist/
# Subir y extraer en el servidor
```

## 🌐 URLs de Producción

- **Frontend**: https://central.wifigo.com.mx
- **Backend API**: https://ccbak.wifigo.com.mx
- **Socket.IO**: wss://ccbak.wifigo.com.mx/socket.io

## 🔧 Configuración del Servidor Web

### Nginx (Recomendado)
```nginx
server {
    listen 80;
    listen 443 ssl;
    server_name central.wifigo.com.mx;
    
    # Configuración SSL
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    # Redirigir HTTP a HTTPS
    if ($scheme != "https") {
        return 301 https://$server_name$request_uri;
    }
    
    # Directorio raíz
    root /var/www/central.wifigo.com.mx;
    index index.html;
    
    # Configuración para SPA (Single Page Application)
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # Cache para assets estáticos
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Configuración de seguridad
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
}
```

### Apache
```apache
<VirtualHost *:80>
    ServerName central.wifigo.com.mx
    Redirect permanent / https://central.wifigo.com.mx/
</VirtualHost>

<VirtualHost *:443>
    ServerName central.wifigo.com.mx
    DocumentRoot /var/www/central.wifigo.com.mx
    
    # Configuración SSL
    SSLEngine on
    SSLCertificateFile /path/to/certificate.crt
    SSLCertificateKeyFile /path/to/private.key
    
    # Configuración para SPA
    <Directory "/var/www/central.wifigo.com.mx">
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted
        
        # Rewrite para SPA
        RewriteEngine On
        RewriteBase /
        RewriteRule ^index\.html$ - [L]
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule . /index.html [L]
    </Directory>
</VirtualHost>
```

## ✅ Verificación Post-Despliegue

### 1. Verificar Frontend
- **URL**: https://central.wifigo.com.mx
- **Debe cargar**: La aplicación React sin errores
- **Consola**: Sin errores de CORS o conexión

### 2. Verificar Conexión con Backend
- **Socket.IO**: Debe conectarse a `wss://ccbak.wifigo.com.mx`
- **API**: Debe hacer requests a `https://ccbak.wifigo.com.mx`
- **Consola**: Mensaje "Socket conectado exitosamente"

### 3. Verificar Funcionalidad
- **Login**: Debe funcionar correctamente
- **Navegación**: Entre páginas sin errores
- **WhatsApp**: Conexión y mensajes funcionando

## 🔍 Troubleshooting

### Problemas Comunes:

1. **Error de CORS**
   - Verificar que el backend esté configurado para aceptar `central.wifigo.com.mx`

2. **Socket.IO no conecta**
   - Verificar que el backend esté corriendo en `ccbak.wifigo.com.mx:3002`
   - Verificar configuración de proxy/firewall

3. **Rutas no funcionan (404)**
   - Configurar correctamente el servidor web para SPA
   - Verificar que `try_files` esté configurado

4. **Assets no cargan**
   - Verificar permisos de archivos
   - Verificar configuración de cache

## 📝 Notas Importantes

- **HTTPS Obligatorio**: El frontend está configurado para usar HTTPS en producción
- **Variables de Entorno**: Se cargan automáticamente desde `.env.production`
- **Build Optimizado**: Los archivos están minificados y optimizados
- **Cache**: Configurar cache apropiado para assets estáticos
- **SSL**: Asegurar certificados SSL válidos para ambos dominios
