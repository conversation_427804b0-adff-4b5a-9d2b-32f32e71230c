import { createClient, WebDAVClient } from 'webdav';
import axios from 'axios';

// Configuración de Nextcloud
const NEXTCLOUD_URL = 'https://apnextcloud.wifigo.com.mx/remote.php/dav/files/nuonetworks';
const NEXTCLOUD_USERNAME = 'nuonetworks';
const NEXTCLOUD_PASSWORD = 'Awifigo12nextcloud_';

// Credenciales de API de Nextcloud
const CLIENT_ID = 'uRlvIKxlicmxbkL8jKPl0Hyuj2De9xMeeg0pQAAMEChjymBcKBmt5koZ49DNLm65';
const CLIENT_SECRET = 'JLJcflkxKJemoAgtKTV09SsCKsi9BUVkBhw9cCqVqR62qhwlNuPxvQUtAJtHkHCo';

// Ruta base para la aplicación (carpeta específica)
const BASE_PATH = '/FINANZAS/FINANZAS/Documentos Finanzas/ADMON Y CONTABILIDAD';

// Token de acceso
let accessToken: string | null = null;
let tokenExpiration: number = 0;

// Interfaz para los archivos y carpetas
export interface NextcloudItem {
  filename: string;
  basename: string;
  lastmod: string;
  size: number;
  type: 'file' | 'directory';
  mime?: string;
  etag?: string;
}

// Función para obtener un token de acceso
const getAccessToken = async (): Promise<string> => {
  // Si ya tenemos un token válido, lo devolvemos
  if (accessToken && tokenExpiration > Date.now()) {
    return accessToken;
  }

  try {
    // Intentar obtener un token usando las credenciales de API
    const tokenUrl = 'https://apnextcloud.wifigo.com.mx/index.php/apps/oauth2/api/v1/token';
    const params = new URLSearchParams();
    params.append('grant_type', 'client_credentials');
    params.append('client_id', CLIENT_ID);
    params.append('client_secret', CLIENT_SECRET);

    const response = await axios.post(tokenUrl, params, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });

    if (response.data && response.data.access_token) {
      accessToken = response.data.access_token;
      // Establecer la expiración (normalmente 1 hora)
      tokenExpiration = Date.now() + (response.data.expires_in || 3600) * 1000;
      return accessToken;
    } else {
      throw new Error('No se pudo obtener el token de acceso');
    }
  } catch (error) {
    console.error('Error al obtener token de acceso:', error);

    // Si falla la autenticación OAuth2, volvemos a la autenticación básica
    console.log('Volviendo a autenticación básica');
    return '';
  }
};

// Crear cliente WebDAV con autenticación
const createWebDAVClient = async (): Promise<WebDAVClient> => {
  try {
    // Intentar obtener un token de acceso
    const token = await getAccessToken();

    if (token) {
      // Crear cliente con token OAuth2
      return createClient(NEXTCLOUD_URL, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
    }
  } catch (error) {
    console.error('Error al crear cliente WebDAV con OAuth:', error);
  }

  // Fallback a autenticación básica
  return createClient(NEXTCLOUD_URL, {
    username: NEXTCLOUD_USERNAME,
    password: NEXTCLOUD_PASSWORD
  });
};

// Cliente WebDAV inicial (se actualizará antes de cada operación)
let client: WebDAVClient = createClient(NEXTCLOUD_URL, {
  username: NEXTCLOUD_USERNAME,
  password: NEXTCLOUD_PASSWORD
});

// Función auxiliar para construir rutas completas
const buildFullPath = (relativePath: string = '/'): string => {
  // Si la ruta relativa es '/', usamos directamente la ruta base
  if (relativePath === '/') {
    return BASE_PATH;
  }

  // Si la ruta relativa ya comienza con la ruta base, la usamos directamente
  if (relativePath.startsWith(BASE_PATH)) {
    return relativePath;
  }

  // Construir la ruta completa
  const fullPath = `${BASE_PATH}${relativePath.startsWith('/') ? '' : '/'}${relativePath}`;
  return fullPath;
};

// Función auxiliar para obtener la ruta relativa a partir de una ruta completa
const getRelativePath = (fullPath: string): string => {
  if (fullPath === BASE_PATH) {
    return '/';
  }

  if (fullPath.startsWith(BASE_PATH)) {
    return fullPath.substring(BASE_PATH.length) || '/';
  }

  return fullPath;
};

// Servicio para interactuar con Nextcloud
export const nextcloudService = {
  // Obtener contenido de una carpeta compartida públicamente
  getSharedFolderContents: async (shareToken: string, path: string = '/'): Promise<NextcloudItem[]> => {
    try {
      // URL para acceder a carpetas compartidas públicamente
      const sharedUrl = `https://apnextcloud.wifigo.com.mx/public.php/webdav`;

      // Crear un cliente específico para la carpeta compartida
      const sharedClient = createClient(sharedUrl, {
        username: shareToken,
        password: '' // No se necesita contraseña para carpetas compartidas públicamente
      });

      console.log(`Obteniendo contenido de carpeta compartida: ${path}`);

      const response = await sharedClient.getDirectoryContents(path);

      // Mapear la respuesta para mantener la consistencia con el resto del servicio
      return Array.isArray(response) ? response.map(item => ({
        filename: item.filename,
        basename: item.basename,
        lastmod: item.lastmod,
        size: item.size,
        type: item.type,
        mime: item.mime,
        etag: item.etag
      })) : [];
    } catch (error) {
      console.error('Error al obtener contenido de carpeta compartida:', error);
      throw error;
    }
  },

  // Descargar un archivo de una carpeta compartida públicamente
  downloadSharedFile: async (shareToken: string, path: string): Promise<ArrayBuffer> => {
    try {
      // URL para acceder a carpetas compartidas públicamente
      const sharedUrl = `https://apnextcloud.wifigo.com.mx/public.php/webdav`;

      // Crear un cliente específico para la carpeta compartida
      const sharedClient = createClient(sharedUrl, {
        username: shareToken,
        password: '' // No se necesita contraseña para carpetas compartidas públicamente
      });

      console.log(`Descargando archivo compartido: ${path}`);

      const response = await sharedClient.getFileContents(path);

      // Verificar si la respuesta es un ArrayBuffer
      if (response instanceof ArrayBuffer) {
        return response;
      } else if (typeof response === 'string') {
        // Convertir string a ArrayBuffer si es necesario
        const encoder = new TextEncoder();
        return encoder.encode(response).buffer;
      }

      throw new Error('Formato de respuesta no soportado');
    } catch (error) {
      console.error('Error al descargar archivo compartido:', error);
      throw error;
    }
  },
  // Obtener contenido de una carpeta
  getDirectoryContents: async (relativePath: string = '/'): Promise<NextcloudItem[]> => {
    try {
      // Actualizar el cliente antes de cada operación
      client = await createWebDAVClient();

      const fullPath = buildFullPath(relativePath);
      console.log(`Obteniendo contenido de: ${fullPath}`);

      const response = await client.getDirectoryContents(fullPath);

      // Verificar si la respuesta es un array (contenido del directorio)
      if (Array.isArray(response)) {
        // Mapear la respuesta a nuestro formato de NextcloudItem
        return response.map(item => ({
          filename: item.filename,
          basename: item.basename,
          lastmod: item.lastmod,
          size: item.size,
          type: item.type,
          mime: item.mime,
          etag: item.etag
        }));
      }

      return [];
    } catch (error) {
      console.error('Error al obtener contenido del directorio:', error);
      throw error;
    }
  },

  // Subir un archivo
  uploadFile: async (relativePath: string, fileContent: ArrayBuffer | Blob): Promise<boolean> => {
    try {
      // Actualizar el cliente antes de cada operación
      client = await createWebDAVClient();

      const fullPath = buildFullPath(relativePath);
      console.log(`Subiendo archivo a: ${fullPath}`);

      await client.putFileContents(fullPath, fileContent);
      return true;
    } catch (error) {
      console.error('Error al subir archivo:', error);
      throw error;
    }
  },

  // Descargar un archivo
  downloadFile: async (relativePath: string): Promise<ArrayBuffer> => {
    try {
      // Actualizar el cliente antes de cada operación
      client = await createWebDAVClient();

      const fullPath = buildFullPath(relativePath);
      console.log(`Descargando archivo desde: ${fullPath}`);

      const response = await client.getFileContents(fullPath);

      // Verificar si la respuesta es un ArrayBuffer
      if (response instanceof ArrayBuffer) {
        return response;
      } else if (typeof response === 'string') {
        // Convertir string a ArrayBuffer si es necesario
        const encoder = new TextEncoder();
        return encoder.encode(response).buffer;
      }

      throw new Error('Formato de respuesta no soportado');
    } catch (error) {
      console.error('Error al descargar archivo:', error);
      throw error;
    }
  },

  // Crear una carpeta
  createDirectory: async (relativePath: string): Promise<boolean> => {
    try {
      // Actualizar el cliente antes de cada operación
      client = await createWebDAVClient();

      const fullPath = buildFullPath(relativePath);
      console.log(`Creando carpeta en: ${fullPath}`);

      await client.createDirectory(fullPath);
      return true;
    } catch (error) {
      console.error('Error al crear carpeta:', error);
      throw error;
    }
  },

  // Eliminar un archivo o carpeta
  deleteItem: async (relativePath: string): Promise<boolean> => {
    try {
      // Actualizar el cliente antes de cada operación
      client = await createWebDAVClient();

      const fullPath = buildFullPath(relativePath);
      console.log(`Eliminando item en: ${fullPath}`);

      await client.deleteFile(fullPath);
      return true;
    } catch (error) {
      console.error('Error al eliminar item:', error);
      throw error;
    }
  },

  // Verificar si la conexión es válida
  testConnection: async (): Promise<boolean> => {
    try {
      // Actualizar el cliente antes de cada operación
      client = await createWebDAVClient();

      // Intentar acceder a la carpeta base
      console.log(`Probando conexión a: ${BASE_PATH}`);
      await client.getDirectoryContents(BASE_PATH);
      return true;
    } catch (error) {
      console.error('Error al probar conexión con Nextcloud:', error);
      return false;
    }
  },

  // Obtener la ruta base
  getBasePath: (): string => {
    return BASE_PATH;
  },

  // Obtener el token de acceso actual (para depuración)
  getAccessTokenInfo: (): { token: string | null, expiresIn: number } => {
    return {
      token: accessToken ? `${accessToken.substring(0, 10)}...` : null,
      expiresIn: tokenExpiration ? Math.floor((tokenExpiration - Date.now()) / 1000) : 0
    };
  }
};
