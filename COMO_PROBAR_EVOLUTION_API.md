# Cómo probar la conexión con Evolution API

Este documento explica cómo probar la conexión con Evolution API antes de iniciar la aplicación completa.

## Requisitos previos

1. Tener Node.js instalado
2. Tener configurado correctamente el archivo `src/backend/evolution-api-config.js`

## Pasos para probar la conexión

### 1. Verificar la configuración

Asegúrate de que el archivo `src/backend/evolution-api-config.js` tenga la configuración correcta:

```javascript
// Configuración para la conexión con Evolution API
const EVOLUTION_API_CONFIG = {
  // URL base de la API de Evolution
  baseUrl: 'https://aaevolutionapi.wifigo.com.mx', // URL de tu instancia de Evolution API
  
  // Clave de API para autenticación
  apiKey: 'wifigo-evolution-api-key', // Reemplaza con tu clave API real
  
  // Instancias de WhatsApp configuradas en Evolution API
  instances: {
    client1: 'wifigo1', // Nombre de la primera instancia en Evolution API
    client2: 'wifigo2'  // Nombre de la segunda instancia en Evolution API
  },
  
  // Tiempo de espera para las solicitudes en milisegundos
  timeout: 60000 // Aumentado a 60 segundos para conexiones más lentas
};
```

Asegúrate de reemplazar:
- `wifigo-evolution-api-key` con tu clave API real
- `wifigo1` y `wifigo2` con los nombres reales de tus instancias en Evolution API

### 2. Ejecutar el script de prueba

Abre una terminal en la carpeta raíz del proyecto y ejecuta:

```bash
cd src/backend
node test-evolution-api.js
```

### 3. Interpretar los resultados

El script mostrará información sobre cada instancia configurada:

- **Estado de la instancia**: Indica si la instancia está conectada, desconectada o en otro estado.
- **Información de usuario**: Si la instancia está conectada, mostrará información sobre el usuario de WhatsApp.
- **Intento de conexión**: Si la instancia no está conectada, el script intentará conectarla.

#### Posibles resultados:

1. **Conexión exitosa**: La instancia está conectada y se muestra la información del usuario.
   ```
   Estado de la instancia:
   - Estado: CONNECTED
   - Conectado: Sí
   - Información de usuario:
     - Nombre: WifiGo
     - Número: 5212345678901
   ```

2. **Instancia no conectada, se genera código QR**:
   ```
   La instancia no está conectada. Intentando conectar...
   Se generó un código QR. Escanéalo con WhatsApp para conectar la instancia.
   ```
   En este caso, deberás escanear el código QR desde la aplicación web.

3. **Error de conexión**:
   ```
   Error al probar la instancia wifigo1: Request failed with status code 404
   ```
   Esto puede indicar que la instancia no existe o que la URL de Evolution API es incorrecta.

### 4. Solución de problemas

#### Error de autenticación
```
Error al probar la instancia wifigo1: Request failed with status code 401
```
Verifica que la clave API en `evolution-api-config.js` sea correcta.

#### Instancia no encontrada
```
Error al probar la instancia wifigo1: Request failed with status code 404
```
Verifica que el nombre de la instancia en `evolution-api-config.js` sea correcto y que la instancia exista en Evolution API.

#### Error de conexión
```
Error al probar la instancia wifigo1: connect ECONNREFUSED
```
Verifica que la URL de Evolution API sea correcta y que el servidor esté en funcionamiento.

#### Timeout
```
Error al probar la instancia wifigo1: timeout of 60000ms exceeded
```
Aumenta el valor de `timeout` en `evolution-api-config.js` o verifica la conexión a internet.

## Próximos pasos

Una vez que hayas verificado que la conexión con Evolution API funciona correctamente, puedes iniciar la aplicación completa:

```bash
npm run dev
```

Y acceder a la interfaz web para ver el estado de las instancias de WhatsApp.
