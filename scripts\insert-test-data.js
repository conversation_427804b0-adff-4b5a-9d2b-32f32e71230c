// Script para insertar datos de prueba en la tabla movimientos_bancarios
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://aasupabase.wifigo.com.mx';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.ewogICJyb2xlIjogImFub24iLAogICJpc3MiOiAic3VwYWJhc2UiLAogICJpYXQiOiAxNzE1MDUwODAwLAogICJleHAiOiAxODcyODE3MjAwCn0.oPxHK-YDuQAv0rveg36Ti0Mf3Rx4LtsJ_CUopnYp9ko';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

const testData = [
  {
    banco: 'BBVA',
    fecha: '2024-01-15',
    concepto: 'Transferencia recibida - Cliente ABC',
    abono: 15000.00,
    cargo: null,
    saldo: 125000.00,
    numero_movimiento: '001'
  },
  {
    banco: 'BBVA',
    fecha: '2024-01-14',
    concepto: 'Pago de servicios - Internet',
    abono: null,
    cargo: 1200.00,
    saldo: 110000.00,
    numero_movimiento: '002'
  },
  {
    banco: 'BBVA',
    fecha: '2024-01-13',
    concepto: 'Depósito en efectivo',
    abono: 5000.00,
    cargo: null,
    saldo: 111200.00,
    numero_movimiento: '003'
  },
  {
    banco: 'BBVA',
    fecha: '2024-01-12',
    concepto: 'Comisión por manejo de cuenta',
    abono: null,
    cargo: 150.00,
    saldo: 106200.00,
    numero_movimiento: '004'
  },
  {
    banco: 'BBVA',
    fecha: '2024-01-11',
    concepto: 'Transferencia enviada - Proveedor XYZ',
    abono: null,
    cargo: 8500.00,
    saldo: 106350.00,
    numero_movimiento: '005'
  }
];

async function insertTestData() {
  try {
    console.log('🔄 Insertando datos de prueba...');
    
    const { data, error } = await supabase
      .from('movimientos_bancarios')
      .insert(testData);
    
    if (error) {
      console.error('❌ Error insertando datos:', error);
      return;
    }
    
    console.log('✅ Datos de prueba insertados exitosamente:', data);
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

insertTestData();
