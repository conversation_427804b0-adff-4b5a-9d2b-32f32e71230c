import { createClient, SupabaseClient } from '@supabase/supabase-js';

// Inicializar el cliente de Supabase con las variables de entorno
const supabaseUrl = 'https://aasupabase.wifigo.com.mx';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.ewogICJyb2xlIjogImFub24iLAogICJpc3MiOiAic3VwYWJhc2UiLAogICJpYXQiOiAxNzE1MDUwODAwLAogICJleHAiOiAxODcyODE3MjAwCn0.oPxHK-YDuQAv0rveg36Ti0Mf3Rx4LtsJ_CUopnYp9ko';

// Opciones personalizadas para el cliente de Supabase
const options = {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  },
  realtime: {
    // Usar la URL de WebSocket directamente
    websocket: {
      url: 'wss://aasupabase.wifigo.com.mx/realtime/v1/websocket',
      params: {
        apikey: supabaseAnonKey,
        vsn: '1.0.0'
      }
    },
    // Configuración adicional para mejorar la estabilidad
    timeout: 30000, // 30 segundos de timeout
    heartbeatIntervalMs: 15000, // Enviar heartbeat cada 15 segundos
    reconnectAfterMs: (tries) => Math.min(2000 * (tries + 1), 20000) // Backoff exponencial
  }
};

// Crear y exportar el cliente de Supabase con opciones personalizadas
export const supabase: SupabaseClient = createClient(
  supabaseUrl,
  supabaseAnonKey,
  options
);
