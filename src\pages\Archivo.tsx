import React, { useState, useEffect, useCallback, useRef } from 'react';
import { supabase } from '../config/supabase';
import axios from 'axios';
import { useTheme } from '../contexts/ThemeContext';

// Interfaces para movimientos bancarios
interface MovimientoBancario {
  id: number;
  banco: string;
  fecha: string;
  concepto: string;
  abono: number | null;
  cargo: number | null;
  saldo: number;
  numero_movimiento: string;
  isExpanded?: boolean;
  contenido?: any; // Contenido adicional que se despliega
}

interface SearchFilters {
  year?: string;
  month?: string;
  bank_type?: string;
}

const Archivo: React.FC = () => {
  // Contexto de tema
  const { isDarkMode, toggleDarkMode } = useTheme();

  // Estados principales
  const [movimientos, setMovimientos] = useState<MovimientoBancario[]>([]);
  const [selectedMovimiento, setSelectedMovimiento] = useState<MovimientoBancario | null>(null);
  const [expandedMovimientos, setExpandedMovimientos] = useState<Set<number>>(new Set());

  // Estados de búsqueda y filtros
  const [globalSearch, setGlobalSearch] = useState('');
  const [localSearch, setLocalSearch] = useState('');
  const [filters, setFilters] = useState<SearchFilters>({});

  // Estados de UI
  const [loading, setLoading] = useState(true);

  // Referencia para el input de archivos
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Estados para archivos pendientes de subir
  interface PendingFile {
    id: string;
    file: File;
    documentType: string;
    base64?: string;
  }

  const [pendingFiles, setPendingFiles] = useState<PendingFile[]>([]);
  const [isProcessingFiles, setIsProcessingFiles] = useState(false);

  // Tipos de documentos disponibles
  const documentTypes = [
    'Comprobante de pago',
    'Recibo emitido a cliente',
    'Cotización',
    'Contrato',
    'Cheque',
    'CFDI'
  ];

  // Cargar movimientos bancarios desde Supabase
  const loadMovimientos = useCallback(async () => {
    try {
      setLoading(true);
      console.log('🔄 Cargando movimientos bancarios desde Supabase...');
      console.log('🔑 Supabase configurado:', !!supabase);

      const { data, error } = await supabase
        .from('movimientos_bancarios')
        .select('*')
        .order('fecha', { ascending: false });

      console.log('📊 Datos recibidos de Supabase:', data);
      console.log('❌ Error de Supabase:', error);
      console.log('📏 Cantidad de movimientos:', data?.length || 0);

      if (error) {
        console.error('🚨 Error detallado:', {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code
        });
        throw error;
      }

      if (!data || data.length === 0) {
        console.log('📭 No hay movimientos bancarios en la base de datos');
        setMovimientos([]);
        return;
      }

      // Agregar estado de expansión a cada movimiento
      const movimientosConEstado = data.map(movimiento => ({
        ...movimiento,
        isExpanded: false
      }));

      console.log('� Movimientos bancarios cargados:', movimientosConEstado.length);
      setMovimientos(movimientosConEstado);
    } catch (error) {
      console.error('❌ Error loading movimientos:', error);
      setMovimientos([]); // Asegurar que se limpia el estado en caso de error
    } finally {
      setLoading(false);
    }
  }, []);

  // Cargar archivos de una carpeta
  const loadFiles = useCallback(async (folderId: number) => {
    try {
      const { data, error } = await supabase
        .from('files')
        .select('*')
        .eq('folder_id', folderId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      
      // Agregar URLs públicas para los archivos
      const filesWithUrls = data?.map(file => ({
        ...file,
        url: file.storage_path ? supabase.storage.from('files').getPublicUrl(file.storage_path).data.publicUrl : undefined
      })) || [];
      
      setCurrentFiles(filesWithUrls);
    } catch (error) {
      console.error('Error loading files:', error);
      setCurrentFiles([]);
    }
  }, []);

  // Test de conexión a Supabase
  const testSupabaseConnection = useCallback(async () => {
    try {
      console.log('🔬 Probando conexión básica a Supabase...');

      // Intentar una consulta simple
      const { data, error, count } = await supabase
        .from('folders')
        .select('*', { count: 'exact' });

      console.log('🧪 Test de conexión - Data:', data);
      console.log('🧪 Test de conexión - Error:', error);
      console.log('🧪 Test de conexión - Count:', count);

      if (error) {
        console.error('🚨 Error en test de conexión:', error);
        return false;
      }

      console.log('✅ Conexión a Supabase exitosa');
      return true;
    } catch (err) {
      console.error('💥 Error crítico en test de conexión:', err);
      return false;
    }
  }, []);

  // Efecto inicial
  useEffect(() => {
    // Primero probar la conexión, luego cargar movimientos
    testSupabaseConnection().then((connected) => {
      if (connected) {
        loadMovimientos();
      } else {
        console.error('❌ No se pudo conectar a Supabase, no se cargarán los movimientos');
      }
    });
  }, [loadMovimientos, testSupabaseConnection]);

  // Función para manejar clic en movimiento (expandir/colapsar)
  const handleMovimientoClick = useCallback((movimiento: MovimientoBancario) => {
    setExpandedMovimientos(prev => {
      const newSet = new Set(prev);
      if (newSet.has(movimiento.id)) {
        newSet.delete(movimiento.id);
      } else {
        newSet.add(movimiento.id);
      }
      return newSet;
    });
  }, []);

  // Función para formatear moneda
  const formatCurrency = (amount: number | null): string => {
    if (amount === null || amount === undefined) return '-';
    return new Intl.NumberFormat('es-MX', {
      style: 'currency',
      currency: 'MXN'
    }).format(amount);
  };







  // Crear nueva carpeta via webhook (función legacy)
  const createFolder = useCallback(async (name: string, parentId?: number) => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      await axios.post(import.meta.env.VITE_WEBHOOK_CREAR_CARPETA, {
        name,
        parent_id: parentId || null,
        created_by: user.id
      });

      loadFolders(); // Recargar carpetas
    } catch (error) {
      console.error('Error creating folder:', error);
    }
  }, [loadFolders]);

  // Subir archivo via webhook
  const uploadFile = useCallback(async (file: File, folderId: number) => {
    try {
      console.log('📤 Subiendo archivo:', file.name);
      console.log('📁 A la carpeta ID:', folderId);

      // Convertir archivo a base64
      const base64Content = await fileToBase64(file);

      const payload = {
        filename: file.name,
        mime_type: file.type,
        size_bytes: file.size,
        folder_id: folderId,
        file_content: base64Content
      };

      console.log('🚀 Enviando archivo al webhook:', {
        filename: payload.filename,
        mime_type: payload.mime_type,
        size_bytes: payload.size_bytes,
        folder_id: payload.folder_id,
        base64_length: base64Content.length
      });

      const webhookUrl = import.meta.env.VITE_WEBHOOK_SUBIR_ARCHIVO;

      await axios.post(webhookUrl, payload, {
        headers: { 'Content-Type': 'application/json' }
      });

      console.log('✅ Archivo subido exitosamente');

      if (currentFolder?.id === folderId) {
        loadFiles(folderId); // Recargar archivos
      }
    } catch (error) {
      console.error('❌ Error uploading file:', error);
    }
  }, [currentFolder, loadFiles]);

  // Eliminar carpeta via webhook
  const deleteFolder = useCallback(async (folderId: number) => {
    try {
      await axios.delete(`${import.meta.env.VITE_WEBHOOK_ELIMINAR_CARPETA}/${folderId}`);
      loadFolders(); // Recargar carpetas
    } catch (error) {
      console.error('Error deleting folder:', error);
    }
  }, [loadFolders]);

  // Eliminar archivo via webhook
  const deleteFile = useCallback(async (fileId: number) => {
    try {
      await axios.delete(`${import.meta.env.VITE_WEBHOOK_ELIMINAR_ARCHIVO}/${fileId}`);
      if (currentFolder) {
        loadFiles(currentFolder.id); // Recargar archivos
      }
    } catch (error) {
      console.error('Error deleting file:', error);
    }
  }, [currentFolder, loadFiles]);

  // Agregar archivos a la lista pendiente
  const addFilesToPending = useCallback((files: File[]) => {
    if (!currentFolder) return;

    console.log('📁 Agregando archivos a lista pendiente:', files.length);

    const newPendingFiles: PendingFile[] = files.map(file => ({
      id: `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      file,
      documentType: documentTypes[0] // Tipo por defecto
    }));

    setPendingFiles(prev => [...prev, ...newPendingFiles]);
  }, [currentFolder, documentTypes]);

  // Manejar selección de archivos desde explorador
  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    if (!currentFolder || !e.target.files) return;

    const files = Array.from(e.target.files);
    console.log('📁 Archivos seleccionados:', files.length);

    addFilesToPending(files);

    // Limpiar el input para permitir seleccionar el mismo archivo de nuevo
    e.target.value = '';
  }, [currentFolder, addFilesToPending]);

  // Manejar drag & drop
  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);

    if (!currentFolder) return;

    const files = Array.from(e.dataTransfer.files);
    console.log('🎯 Archivos arrastrados:', files.length);

    addFilesToPending(files);
  }, [currentFolder, addFilesToPending]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  // Abrir explorador de archivos
  const openFileExplorer = useCallback(() => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  }, []);

  // Actualizar tipo de documento de un archivo pendiente
  const updateDocumentType = useCallback((fileId: string, documentType: string) => {
    setPendingFiles(prev =>
      prev.map(file =>
        file.id === fileId ? { ...file, documentType } : file
      )
    );
  }, []);

  // Remover archivo de la lista pendiente
  const removePendingFile = useCallback((fileId: string) => {
    setPendingFiles(prev => prev.filter(file => file.id !== fileId));
  }, []);

  // Convertir archivo a base64
  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        if (typeof reader.result === 'string') {
          // Remover el prefijo "data:mime/type;base64," para obtener solo el base64
          const base64 = reader.result.split(',')[1];
          resolve(base64);
        } else {
          reject(new Error('Error al convertir archivo a base64'));
        }
      };
      reader.onerror = (error) => reject(error);
    });
  };

  // Subir todos los archivos pendientes
  const uploadAllPendingFiles = useCallback(async () => {
    if (!currentFolder || pendingFiles.length === 0) return;

    setIsProcessingFiles(true);
    console.log('🚀 Subiendo archivos pendientes:', pendingFiles.length);

    try {
      for (const pendingFile of pendingFiles) {
        console.log('📤 Subiendo archivo:', pendingFile.file.name);
        console.log('📋 Tipo de documento:', pendingFile.documentType);

        // Convertir archivo a base64
        const base64Content = await fileToBase64(pendingFile.file);

        const payload = {
          filename: pendingFile.file.name,
          mime_type: pendingFile.file.type,
          size_bytes: pendingFile.file.size,
          folder_id: currentFolder.id,
          file_content: base64Content,
          document_type: pendingFile.documentType
        };

        console.log('🚀 Enviando archivo al webhook:', {
          filename: payload.filename,
          mime_type: payload.mime_type,
          size_bytes: payload.size_bytes,
          folder_id: payload.folder_id,
          document_type: payload.document_type,
          base64_length: base64Content.length
        });

        const webhookUrl = import.meta.env.VITE_WEBHOOK_SUBIR_ARCHIVO;

        await axios.post(webhookUrl, payload, {
          headers: { 'Content-Type': 'application/json' }
        });

        console.log('✅ Archivo subido exitosamente:', pendingFile.file.name);
      }

      // Limpiar lista de archivos pendientes
      setPendingFiles([]);

      // Recargar archivos de la carpeta
      loadFiles(currentFolder.id);

      console.log('🎉 Todos los archivos subidos exitosamente');
    } catch (error) {
      console.error('❌ Error uploading files:', error);
    } finally {
      setIsProcessingFiles(false);
    }
  }, [currentFolder, pendingFiles, fileToBase64, loadFiles]);

  // Obtener icono de archivo basado en mime_type
  const getFileIcon = (mimeType: string): string => {
    if (mimeType.startsWith('image/')) return '🖼️';
    if (mimeType === 'application/pdf') return '📄';
    if (mimeType.includes('word') || mimeType.includes('document')) return '📝';
    if (mimeType.includes('sheet') || mimeType.includes('excel')) return '📊';
    if (mimeType.includes('zip') || mimeType.includes('rar')) return '🗜️';
    if (mimeType.startsWith('video/')) return '🎥';
    if (mimeType.startsWith('audio/')) return '🎵';
    return '📄';
  };

  // Formatear tamaño de archivo
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Obtener colores según el tema (sincronizado exactamente con CustomerService)
  const getThemeColors = () => {
    if (isDarkMode) {
      return {
        background: '#111827', // bg-gray-900 - EXACTAMENTE igual que CustomerService
        cardBackground: 'rgba(31, 41, 55, 0.3)', // bg-gray-800/30 - EXACTAMENTE igual que CustomerService
        border: 'rgba(75, 85, 99, 0.3)', // border-gray-600/30 - EXACTAMENTE igual que CustomerService
        text: '#f9fafb', // text-gray-50 - mismo que CustomerService
        textSecondary: '#d1d5db', // text-gray-300 - mismo que CustomerService
        accent: '#3ca2a2', // mantener el color de acento original
        accentHover: '#215a6d', // mantener el hover original
        secondary: 'rgba(107, 114, 128, 0.5)', // bg-gray-500/50 - transparente como CustomerService
        secondaryHover: 'rgba(75, 85, 99, 0.5)' // bg-gray-600/50 - hover transparente
      };
    } else {
      return {
        background: '#dfece6',
        cardBackground: '#ffffff',
        border: '#92c7a3',
        text: '#2d2d29',
        textSecondary: '#215a6d',
        accent: '#3ca2a2',
        accentHover: '#215a6d',
        secondary: '#92c7a3',
        secondaryHover: '#3ca2a2'
      };
    }
  };

  const colors = getThemeColors();

  // Obtener todas las carpetas en formato plano para el selector
  const getAllFoldersFlat = useCallback((folderList: Folder[], level: number = 0): Array<{folder: Folder, level: number}> => {
    const result: Array<{folder: Folder, level: number}> = [];

    folderList.forEach(folder => {
      result.push({ folder, level });
      if (folder.children && folder.children.length > 0) {
        result.push(...getAllFoldersFlat(folder.children, level + 1));
      }
    });

    return result;
  }, []);

  // Renderizar árbol de carpetas
  const renderFolderTree = (folderList: Folder[], level: number = 0) => {
    return folderList.map(folder => (
      <div key={folder.id} className="select-none">
        {/* Carpeta principal */}
        <div
          className="group flex items-center py-2 px-3 cursor-pointer rounded transition-colors"
          style={{
            backgroundColor: currentFolder?.id === folder.id ? colors.secondary : 'transparent',
            color: currentFolder?.id === folder.id ? colors.cardBackground : colors.text,
            paddingLeft: `${level * 20 + 12}px`
          }}
          onMouseEnter={(e) => {
            if (currentFolder?.id !== folder.id) {
              e.currentTarget.style.backgroundColor = colors.secondary;
              e.currentTarget.style.color = colors.cardBackground;
            }
          }}
          onMouseLeave={(e) => {
            if (currentFolder?.id !== folder.id) {
              e.currentTarget.style.backgroundColor = 'transparent';
              e.currentTarget.style.color = colors.text;
            }
          }}
          onClick={() => handleFolderClick(folder)}
          onDoubleClick={() => handleFolderClick(folder, true)}
        >
          <span className="mr-2">
            {folder.children && folder.children.length > 0 ? '📁' : '📂'}
          </span>
          <span className="text-sm font-medium truncate flex-1">{folder.name}</span>
          {folder.children && folder.children.length > 0 && (
            <span className="ml-2 text-xs" style={{ color: colors.textSecondary }}>
              ({folder.children.length})
            </span>
          )}
          {/* Botón eliminar carpeta */}
          <button
            onClick={(e) => {
              e.stopPropagation();
              if (confirm(`¿Estás seguro de que deseas eliminar la carpeta "${folder.name}"?`)) {
                deleteFolder(folder.id);
              }
            }}
            className="ml-2 opacity-0 group-hover:opacity-100 transition-opacity text-xs px-1 py-1 rounded"
            style={{ color: colors.textSecondary }}
            onMouseEnter={(e) => e.currentTarget.style.color = colors.accent}
            onMouseLeave={(e) => e.currentTarget.style.color = colors.textSecondary}
          >
            🗑️
          </button>
        </div>

        {/* Renderizar hijos si existen */}
        {folder.children && folder.children.length > 0 && (
          <div>
            {renderFolderTree(folder.children, level + 1)}
          </div>
        )}

        {/* Mostrar archivos si la carpeta está expandida (solo para carpetas finales) */}
        {expandedFolders.has(folder.id) && (!folder.children || folder.children.length === 0) && (
          <div style={{ paddingLeft: `${(level + 1) * 20 + 12}px` }}>
            {currentFiles.slice(0, 3).map(file => (
              <div
                key={file.id}
                className="flex items-center py-1 px-3 cursor-pointer rounded text-sm transition-colors"
                style={{ color: colors.textSecondary }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = colors.secondary;
                  e.currentTarget.style.color = colors.cardBackground;
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                  e.currentTarget.style.color = colors.textSecondary;
                }}
                onClick={() => handleFileClick(file)}
              >
                <span className="mr-2">{getFileIcon(file.mime_type)}</span>
                <span className="truncate">{file.filename}</span>
              </div>
            ))}
            {currentFiles.length > 3 && (
              <div
                className="text-xs px-3 py-1"
                style={{
                  color: colors.textSecondary,
                  paddingLeft: `${(level + 2) * 20 + 12}px`
                }}
              >
                ... y {currentFiles.length - 3} archivos más
              </div>
            )}
          </div>
        )}
      </div>
    ));
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{ backgroundColor: colors.background }}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 mx-auto mb-4" style={{ borderColor: colors.accent }}></div>
          <p style={{ color: colors.textSecondary }}>Cargando sistema de archivos...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col" style={{ backgroundColor: colors.cardBackground }}>
      {/* Contenedor principal unificado con bordes */}
      <div className="flex flex-col flex-1 mx-4 mt-4 mb-4 rounded-lg overflow-hidden" style={{ backgroundColor: colors.cardBackground, border: `1px solid ${colors.border}` }}>
        {/* Header superior con título, búsqueda global y toggle tema */}
        <div className="border-b p-4" style={{ borderColor: colors.border }}>
          <div className="flex justify-between items-center mb-4">
            {/* Título pegado a la izquierda */}
            <h1 className="text-2xl font-bold" style={{ color: colors.text }}>
              Filtros
            </h1>

            {/* Controles de la derecha */}
            <div className="flex items-center gap-3">
              {/* Búsqueda global con lupa */}
              <div className="relative">
                {!showGlobalSearch ? (
                  <button
                    onClick={() => setShowGlobalSearch(true)}
                    className="p-2 rounded-lg transition-colors"
                    style={{
                      color: colors.text
                    }}
                    onMouseEnter={(e) => e.currentTarget.style.backgroundColor = colors.secondary}
                    onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                  >
                    🔍
                  </button>
                ) : (
                  <div className="flex items-center">
                    <input
                      type="text"
                      placeholder="Búsqueda global..."
                      value={globalSearch}
                      onChange={(e) => setGlobalSearch(e.target.value)}
                      className="px-3 py-2 border rounded-l-lg focus:outline-none focus:ring-2 w-64"
                      style={{
                        borderColor: colors.border,
                        color: colors.text,
                        backgroundColor: colors.cardBackground
                      }}
                      autoFocus
                    />
                    <button
                      onClick={() => {
                        setShowGlobalSearch(false);
                        setGlobalSearch('');
                      }}
                      className="px-3 py-2 border-l-0 border rounded-r-lg transition-colors"
                      style={{
                        borderColor: colors.border,
                        color: colors.text
                      }}
                      onMouseEnter={(e) => e.currentTarget.style.backgroundColor = colors.secondary}
                      onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                    >
                      ✕
                    </button>
                  </div>
                )}
              </div>

              {/* Botón toggle tema */}
              <button
                onClick={toggleDarkMode}
                className="p-2 rounded-lg transition-colors"
                style={{
                  color: colors.text
                }}
                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = colors.secondary}
                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
              >
                {isDarkMode ? '☀️' : '🌙'}
              </button>
            </div>
          </div>

          {/* Búsqueda local y filtros centrados en una sola línea */}
          <div className="flex justify-center items-center gap-4 px-4 pb-4">
            {/* Búsqueda local */}
            <input
              type="text"
              placeholder={currentFolder ? `🔍 Buscar en ${currentFolder.name}...` : "🔍 Selecciona una carpeta para buscar localmente"}
              value={localSearch}
              onChange={(e) => setLocalSearch(e.target.value)}
              disabled={!currentFolder}
              className="px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 w-80"
              style={{
                borderColor: colors.border,
                color: colors.text,
                backgroundColor: colors.cardBackground
              }}
            />

            {/* Filtros */}
            <select
              value={filters.year || ''}
              onChange={(e) => setFilters(prev => ({ ...prev, year: e.target.value || undefined }))}
              className="px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 w-32"
              style={{
                borderColor: colors.border,
                color: colors.text,
                backgroundColor: colors.cardBackground
              }}
            >
              <option value="">📅 Año</option>
              <option value="2025">2025</option>
              <option value="2024">2024</option>
              <option value="2023">2023</option>
            </select>

            <select
              value={filters.month || ''}
              onChange={(e) => setFilters(prev => ({ ...prev, month: e.target.value || undefined }))}
              className="px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 w-36"
              style={{
                borderColor: colors.border,
                color: colors.text,
                backgroundColor: colors.cardBackground
              }}
            >
              <option value="">📅 Mes</option>
              <option value="ENERO">Enero</option>
              <option value="FEBRERO">Febrero</option>
              <option value="MARZO">Marzo</option>
              <option value="ABRIL">Abril</option>
              <option value="MAYO">Mayo</option>
              <option value="JUNIO">Junio</option>
              <option value="JULIO">Julio</option>
              <option value="AGOSTO">Agosto</option>
              <option value="SEPTIEMBRE">Septiembre</option>
              <option value="OCTUBRE">Octubre</option>
              <option value="NOVIEMBRE">Noviembre</option>
              <option value="DICIEMBRE">Diciembre</option>
            </select>

            <select
              value={filters.bank_type || ''}
              onChange={(e) => setFilters(prev => ({ ...prev, bank_type: e.target.value || undefined }))}
              className="px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 w-36"
              style={{
                borderColor: colors.border,
                color: colors.text,
                backgroundColor: colors.cardBackground
              }}
            >
              <option value="">🏦 Banco</option>
              <option value="BBVA">BBVA</option>
              <option value="SANTANDER">Santander</option>
              <option value="BANAMEX">Banamex</option>
              <option value="BANORTE">Banorte</option>
              <option value="HSBC">HSBC</option>
            </select>

            <button
              onClick={() => setFilters({})}
              className="px-4 py-2 rounded-lg transition-colors border"
              style={{
                borderColor: colors.border,
                color: colors.text
              }}
              onMouseEnter={(e) => e.currentTarget.style.backgroundColor = colors.secondary}
              onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
            >
              🗑️
            </button>
          </div>
        </div>

        {/* Contenido principal - Vista de tabla */}
        <div className="flex flex-1 overflow-hidden">
          {/* Panel principal - Vista de tabla de movimientos bancarios */}
          <div className="w-full flex flex-col">
            {/* Header de la tabla */}
            <div className="flex items-center justify-between p-4 border-b" style={{ borderColor: colors.border }}>
              <h2 className="text-lg font-semibold" style={{ color: colors.textSecondary }}>
                � Movimientos Bancarios
              </h2>
            </div>

            {/* Encabezados de columnas */}
            <div className="flex items-center px-4 py-3 border-b text-sm font-medium" style={{ borderColor: colors.border, color: colors.textSecondary }}>
              <div className="w-8"></div> {/* Espacio para icono de carpeta */}
              <div className="w-24">Banco</div>
              <div className="w-28">Fecha</div>
              <div className="flex-1">Concepto</div>
              <div className="w-24 text-right">Abono</div>
              <div className="w-24 text-right">Cargo</div>
              <div className="w-24 text-right">Saldo</div>
              <div className="w-32">Número</div>
            </div>

            {/* Lista de carpetas y archivos */}
            <div className="flex-1 overflow-y-auto">
              {loading ? (
                <div className="flex items-center justify-center h-64">
                  <div className="text-center">
                    <div className="text-3xl mb-2">⏳</div>
                    <p className="text-sm" style={{ color: colors.textSecondary }}>
                      Cargando movimientos bancarios...
                    </p>
                  </div>
                </div>
              ) : movimientos.length === 0 ? (
                <div className="flex items-center justify-center h-64">
                  <div className="text-center">
                    <div className="text-3xl mb-2">�</div>
                    <p className="text-sm font-medium mb-1" style={{ color: colors.text }}>
                      No hay movimientos bancarios
                    </p>
                    <p className="text-xs" style={{ color: colors.textSecondary }}>
                      Los movimientos aparecerán aquí cuando estén disponibles
                    </p>
                  </div>
                </div>
              ) : (
                /* Renderizar movimientos bancarios */
                movimientos.map((movimiento, index) => (
                  <div key={movimiento.id}>
                    {/* Fila principal del movimiento */}
                    <div
                      className="flex items-center px-4 py-3 hover:bg-opacity-50 cursor-pointer border-b transition-colors"
                      style={{
                        borderColor: colors.border,
                        backgroundColor: expandedMovimientos.has(movimiento.id) ? colors.secondary : 'transparent'
                      }}
                      onClick={() => handleMovimientoClick(movimiento)}
                      onMouseEnter={(e) => {
                        if (!expandedMovimientos.has(movimiento.id)) {
                          e.currentTarget.style.backgroundColor = colors.secondary;
                        }
                      }}
                      onMouseLeave={(e) => {
                        if (!expandedMovimientos.has(movimiento.id)) {
                          e.currentTarget.style.backgroundColor = 'transparent';
                        }
                      }}
                    >
                      {/* Icono de carpeta */}
                      <div className="w-8 flex justify-center">
                        <span className="text-lg">
                          {expandedMovimientos.has(movimiento.id) ? '📂' : '📁'}
                        </span>
                      </div>

                      {/* Banco */}
                      <div className="w-24 text-sm truncate" style={{ color: colors.text }}>
                        {movimiento.banco}
                      </div>

                      {/* Fecha */}
                      <div className="w-28 text-sm" style={{ color: colors.textSecondary }}>
                        {new Date(movimiento.fecha).toLocaleDateString('es-ES')}
                      </div>

                      {/* Concepto */}
                      <div className="flex-1 text-sm truncate pr-2" style={{ color: colors.text }}>
                        {movimiento.concepto}
                      </div>

                      {/* Abono */}
                      <div className="w-24 text-right text-sm" style={{
                        color: movimiento.abono ? '#10b981' : colors.textSecondary
                      }}>
                        {formatCurrency(movimiento.abono)}
                      </div>

                      {/* Cargo */}
                      <div className="w-24 text-right text-sm" style={{
                        color: movimiento.cargo ? '#ef4444' : colors.textSecondary
                      }}>
                        {formatCurrency(movimiento.cargo)}
                      </div>

                      {/* Saldo */}
                      <div className="w-24 text-right text-sm font-medium" style={{ color: colors.text }}>
                        {formatCurrency(movimiento.saldo)}
                      </div>

                      {/* Número de movimiento */}
                      <div className="w-32 text-sm" style={{ color: colors.textSecondary }}>
                        {movimiento.numero_movimiento}
                      </div>
                    </div>

                    {/* Contenido expandido */}
                    {expandedMovimientos.has(movimiento.id) && (
                      <div
                        className="px-4 py-6 border-b"
                        style={{
                          backgroundColor: colors.cardBackground,
                          borderColor: colors.border
                        }}
                      >
                        <div className="max-w-4xl">
                          <h4 className="text-sm font-medium mb-3" style={{ color: colors.text }}>
                            📄 Contenido del movimiento #{movimiento.numero_movimiento}
                          </h4>
                          <div className="bg-gray-50 rounded-lg p-4 text-sm" style={{
                            backgroundColor: colors.secondary,
                            color: colors.textSecondary
                          }}>
                            <p>Aquí se mostraría el contenido detallado del movimiento bancario.</p>
                            <p className="mt-2">
                              <strong>Banco:</strong> {movimiento.banco}<br/>
                              <strong>Fecha:</strong> {new Date(movimiento.fecha).toLocaleDateString('es-ES')}<br/>
                              <strong>Concepto:</strong> {movimiento.concepto}<br/>
                              <strong>Saldo:</strong> {formatCurrency(movimiento.saldo)}
                            </p>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ))
              )}


            </div>
          </div>

        </div>
      </div>
    </div>
  );
};

export default Archivo;
