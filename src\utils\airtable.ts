const AIRTABLE_API_KEY = '**********************************************************************************';

// IDs de tus bases Airtable
const BASE_BBVA = 'appaRj4QxqWIh2IcF';
const BASE_COINCIDENCIAS = 'apprfXH7G5SgHDcjW';
const BASE_SANTANDER = 'appZAH4Yd3uY5MeJn';
const base_central = 'appaCKW5fX9pf0JRQ';

// 🔧 Función genérica para consultas
const fetchAirtable = async (
  baseId: string,
  table: string,
  options: {
    fields?: string[];
    filter?: string;
    sort?: { field: string; direction: 'asc' | 'desc' }[];
  } = {}
) => {
  const baseUrl = `https://api.airtable.com/v0/${baseId}/${encodeURIComponent(table)}`;
  const params = new URLSearchParams();

  if (options.filter) params.append('filterByFormula', options.filter);
  if (options.fields) {
    for (const field of options.fields) {
      params.append('fields[]', field);
    }
  }
  if (options.sort) {
    options.sort.forEach((s, index) => {
      params.append(`sort[${index}][field]`, s.field);
      params.append(`sort[${index}][direction]`, s.direction);
    });
  }

  const response = await fetch(`${baseUrl}?${params.toString()}`, {
    headers: {
      Authorization: `Bearer ${AIRTABLE_API_KEY}`,
    },
  });

  const data = await response.json();
  return data.records.map((r: any) => r.fields);
};

// ✅ Movimientos BBVA no chequeados y con abono válido, ordenados por ID DESC
export const getBBVAMovements = async () => {
  return await fetchAirtable(BASE_BBVA, 'Movimientos BBVA', {
    fields: ['Dia', 'Concepto / Referencia', 'Abono', 'ID'], // Añadido el campo ID
    filter: `AND({chequeo} = "", NOT({Abono} = BLANK()))`,
    sort: [{ field: 'ID', direction: 'desc' }],
  });
};

// ✅ Movimientos Santander no chequeados y con importe válido, ordenados por ID DESC
export const getSantanderMovements = async () => {
  return await fetchAirtable('appZAH4Yd3uY5MeJn', 'Movimientos Santander', {
    fields: ['Fecha', 'Concepto', 'Importe', 'ID'], // Campos específicos de Santander
    filter: `AND({chequeo} = "", NOT({Importe} = BLANK()))`,
    sort: [{ field: 'ID', direction: 'desc' }],
  });
};

// ✅ Tickets recibidos (URLs e imágenes base64, donde "check list" está vacío)
export const getTickets = async () => {
  return await fetchAirtable(BASE_COINCIDENCIAS, 'Tickets recibidos', {
    fields: ['url', 'imagen_base64'],
    filter: `OR({check list} = "", {check list} = BLANK())`,
  });
};

// ✅ Subir una imagen como nuevo ticket
export const uploadTicketImage = async (imageData: string, fileName: string): Promise<string | null> => {
  try {
    // Preparar la URL para la API de Airtable
    const url = `https://api.airtable.com/v0/${BASE_COINCIDENCIAS}/Tickets%20recibidos`;

    // Determinar el tipo MIME basado en la extensión del archivo
    let mimeType = 'image/jpeg'; // Por defecto
    const extension = fileName.split('.').pop()?.toLowerCase();
    if (extension === 'png') mimeType = 'image/png';
    if (extension === 'gif') mimeType = 'image/gif';
    if (extension === 'webp') mimeType = 'image/webp';

    console.log(`Subiendo imagen ${fileName} con tipo MIME: ${mimeType}`);

    // Crear el cuerpo de la solicitud con la imagen en base64
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${AIRTABLE_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        records: [
          {
            fields: {
              'Nombre': fileName,
              'url': `data:${mimeType};base64,${imageData}`,
            },
          },
        ],
      }),
    });

    const data = await response.json();
    console.log('Respuesta de Airtable al subir imagen:', data);

    // Verificar si la respuesta contiene el registro creado
    if (data.records && data.records.length > 0 && data.records[0].fields.url) {
      // Guardar la URL completa para mostrar la imagen
      const imageUrl = data.records[0].fields.url;

      // También guardar el ID del registro para usarlo en relaciones
      const recordId = data.records[0].id;
      console.log(`Imagen subida exitosamente. ID del registro: ${recordId}`);

      return imageUrl;
    }

    console.error('Error: No se pudo obtener la URL de la imagen subida', data);
    return null;
  } catch (error) {
    console.error('Error al subir imagen a Airtable:', error);
    return null;
  }
};


// ✅ Crear registro en Registro de coincidencias (usando webhook)
export const createCoincidenceRecord = async (data: {
  dia: string;
  concepto: string;
  abono: string | number; // Acepta tanto string como number
  url: string;
  id_movimiento?: string; // ID del movimiento como string (convertido de número)
  banco?: string; // Banco del movimiento (bbva o santander)
}) => {
  const webhookUrl = 'https://aawebhook.wifigo.com.mx/webhook/registrar-coincidencia';

  // Asegurar que abono se envía como string
  const abonoAsString = String(data.abono);

  // Extraer la parte base64 de la URL si es una cadena base64
  let imageBase64 = '';
  if (data.url.startsWith('data:')) {
    // Extraer solo la parte base64 (sin el prefijo "data:image/jpeg;base64,")
    imageBase64 = data.url.split(',')[1];
  }

  console.log('Enviando datos de coincidencia al webhook:', {
    dia: data.dia,
    concepto: data.concepto,
    abono: abonoAsString,
    id_movimiento: data.id_movimiento || 'No disponible',
    banco: data.banco || 'bbva', // Por defecto es bbva si no se especifica
    url_preview: data.url.substring(0, 50) + '...' // Mostrar solo parte de la URL para no saturar la consola
  });

  try {
    // Preparar los datos para el webhook
    const webhookData = {
      dia: data.dia,
      concepto: data.concepto,
      abono: abonoAsString,
      id_movimiento: data.id_movimiento || '', // Incluir el ID del movimiento
      banco: data.banco || 'bbva', // Incluir el banco (por defecto bbva)
      imagen_base64: imageBase64, // Enviar la parte base64 de la imagen
      url_completa: data.url // Enviar la URL completa por si es necesaria
    };

    // Enviar los datos al webhook
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(webhookData),
    });

    // Verificar si la respuesta es exitosa
    if (!response.ok) {
      let errorMessage = 'Error al enviar datos al webhook';

      try {
        const errorData = await response.json();
        console.error('Error detallado del webhook:', errorData);
        errorMessage = errorData.message || errorMessage;
      } catch (parseError) {
        console.error('No se pudo parsear la respuesta de error:', parseError);
      }

      console.error(`Error (${response.status}): ${errorMessage}`);
      return false;
    }

    // Intentar obtener la respuesta JSON
    try {
      const responseData = await response.json();
      console.log('Respuesta del webhook:', responseData);
    } catch (parseError) {
      // Si no es JSON, mostrar el texto de la respuesta
      const responseText = await response.text();
      console.log('Respuesta del webhook (texto):', responseText);
    }

    console.log('Datos de coincidencia enviados exitosamente al webhook');
    return true;
  } catch (error) {
    console.error('Error al enviar datos al webhook:', error);
    return false;
  }
};
