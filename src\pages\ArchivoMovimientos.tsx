import React, { useState, useEffect, useCallback } from 'react';
import { supabase } from '../config/supabase';
import { getThemeColors } from '../utils/theme';
import { useTheme } from '../contexts/ThemeContext';

// Tipos
interface MovimientoBancario {
  id: number;
  banco: string;
  fecha: string;
  concepto: string;
  abono: number | null;
  cargo: number | null;
  saldo: number;
  numero_movimiento: string;
}

const ArchivoMovimientos: React.FC = () => {
  // Contexto de tema
  const { isDarkMode } = useTheme();

  // Estados
  const [movimientos, setMovimientos] = useState<MovimientoBancario[]>([]);
  const [expandedMovimientos, setExpandedMovimientos] = useState<Set<number>>(new Set());
  const [loading, setLoading] = useState(false);

  // Probar conexión a Supabase
  const testSupabaseConnection = useCallback(async () => {
    try {
      console.log('🧪 Probando conexión a Supabase...');
      const { data, error } = await supabase.from('movimientos_bancarios').select('count').limit(1);
      
      if (error) {
        console.error('❌ Error de conexión:', error);
        return false;
      }
      
      console.log('✅ Conexión exitosa a Supabase');
      return true;
    } catch (error) {
      console.error('❌ Error de conexión:', error);
      return false;
    }
  }, []);

  // Cargar movimientos bancarios desde Supabase
  const loadMovimientos = useCallback(async () => {
    try {
      setLoading(true);
      console.log('🔄 Cargando movimientos bancarios desde Supabase...');

      const { data, error } = await supabase
        .from('movimientos_bancarios')
        .select('*')
        .order('fecha', { ascending: false });

      console.log('📊 Datos recibidos:', data);
      console.log('❌ Error:', error);

      if (error) {
        console.error('🚨 Error detallado:', error);
        throw error;
      }

      if (!data || data.length === 0) {
        console.log('📭 No hay movimientos bancarios');
        setMovimientos([]);
        return;
      }

      console.log('💰 Movimientos bancarios cargados:', data.length);
      setMovimientos(data);
    } catch (error) {
      console.error('❌ Error loading movimientos:', error);
      setMovimientos([]);
    } finally {
      setLoading(false);
    }
  }, []);

  // Efecto inicial
  useEffect(() => {
    testSupabaseConnection().then((connected) => {
      if (connected) {
        loadMovimientos();
      } else {
        console.error('❌ No se pudo conectar a Supabase');
      }
    });
  }, [loadMovimientos, testSupabaseConnection]);

  // Función para manejar clic en movimiento (expandir/colapsar)
  const handleMovimientoClick = useCallback((movimiento: MovimientoBancario) => {
    setExpandedMovimientos(prev => {
      const newSet = new Set(prev);
      if (newSet.has(movimiento.id)) {
        newSet.delete(movimiento.id);
      } else {
        newSet.add(movimiento.id);
      }
      return newSet;
    });
  }, []);

  // Función para formatear moneda
  const formatCurrency = (amount: number | null): string => {
    if (amount === null || amount === undefined) return '-';
    return new Intl.NumberFormat('es-MX', {
      style: 'currency',
      currency: 'MXN'
    }).format(amount);
  };

  // Obtener colores del tema
  const colors = getThemeColors(isDarkMode);

  return (
    <div className="min-h-screen" style={{ backgroundColor: colors.background }}>
      {/* Header */}
      <div className="border-b" style={{ borderColor: colors.border, backgroundColor: colors.cardBackground }}>
        <div className="px-6 py-4">
          <h1 className="text-2xl font-bold" style={{ color: colors.text }}>
            💰 Movimientos Bancarios
          </h1>
          <p className="text-sm mt-1" style={{ color: colors.textSecondary }}>
            Gestión de movimientos bancarios con vista expandible
          </p>
        </div>
      </div>

      {/* Contenido principal */}
      <div className="flex flex-1 overflow-hidden">
        <div className="w-full flex flex-col">
          {/* Tabla de movimientos */}
          <div className="flex-1 rounded-lg border m-6" style={{ borderColor: colors.border, backgroundColor: colors.cardBackground }}>
            
            {/* Header de la tabla */}
            <div className="flex items-center justify-between p-4 border-b" style={{ borderColor: colors.border }}>
              <h2 className="text-lg font-semibold" style={{ color: colors.textSecondary }}>
                💰 Movimientos Bancarios
              </h2>
            </div>

            {/* Encabezados de columnas */}
            <div className="flex items-center px-4 py-3 border-b text-sm font-medium" style={{ borderColor: colors.border, color: colors.textSecondary }}>
              <div className="w-8"></div> {/* Espacio para icono de carpeta */}
              <div className="w-24">Banco</div>
              <div className="w-28">Fecha</div>
              <div className="flex-1">Concepto</div>
              <div className="w-24 text-right">Abono</div>
              <div className="w-24 text-right">Cargo</div>
              <div className="w-24 text-right">Saldo</div>
              <div className="w-32">Número</div>
            </div>

            {/* Lista de movimientos bancarios */}
            <div className="flex-1 overflow-y-auto">
              {loading ? (
                <div className="flex items-center justify-center h-64">
                  <div className="text-center">
                    <div className="text-3xl mb-2">⏳</div>
                    <p className="text-sm" style={{ color: colors.textSecondary }}>
                      Cargando movimientos bancarios...
                    </p>
                  </div>
                </div>
              ) : movimientos.length === 0 ? (
                <div className="flex items-center justify-center h-64">
                  <div className="text-center">
                    <div className="text-3xl mb-2">📊</div>
                    <p className="text-sm font-medium mb-1" style={{ color: colors.text }}>
                      No hay movimientos bancarios
                    </p>
                    <p className="text-xs" style={{ color: colors.textSecondary }}>
                      Los movimientos aparecerán aquí cuando estén disponibles
                    </p>
                  </div>
                </div>
              ) : (
                /* Renderizar movimientos bancarios */
                movimientos.map((movimiento) => (
                  <div key={movimiento.id}>
                    {/* Fila principal del movimiento */}
                    <div
                      className="flex items-center px-4 py-3 hover:bg-opacity-50 cursor-pointer border-b transition-colors"
                      style={{
                        borderColor: colors.border,
                        backgroundColor: expandedMovimientos.has(movimiento.id) ? colors.secondary : 'transparent'
                      }}
                      onClick={() => handleMovimientoClick(movimiento)}
                      onMouseEnter={(e) => {
                        if (!expandedMovimientos.has(movimiento.id)) {
                          e.currentTarget.style.backgroundColor = colors.secondary;
                        }
                      }}
                      onMouseLeave={(e) => {
                        if (!expandedMovimientos.has(movimiento.id)) {
                          e.currentTarget.style.backgroundColor = 'transparent';
                        }
                      }}
                    >
                      {/* Icono de carpeta */}
                      <div className="w-8 flex justify-center">
                        <span className="text-lg">
                          {expandedMovimientos.has(movimiento.id) ? '📂' : '📁'}
                        </span>
                      </div>
                      
                      {/* Banco */}
                      <div className="w-24 text-sm truncate" style={{ color: colors.text }}>
                        {movimiento.banco}
                      </div>
                      
                      {/* Fecha */}
                      <div className="w-28 text-sm" style={{ color: colors.textSecondary }}>
                        {new Date(movimiento.fecha).toLocaleDateString('es-ES')}
                      </div>
                      
                      {/* Concepto */}
                      <div className="flex-1 text-sm truncate pr-2" style={{ color: colors.text }}>
                        {movimiento.concepto}
                      </div>
                      
                      {/* Abono */}
                      <div className="w-24 text-right text-sm" style={{ 
                        color: movimiento.abono ? '#10b981' : colors.textSecondary 
                      }}>
                        {formatCurrency(movimiento.abono)}
                      </div>
                      
                      {/* Cargo */}
                      <div className="w-24 text-right text-sm" style={{ 
                        color: movimiento.cargo ? '#ef4444' : colors.textSecondary 
                      }}>
                        {formatCurrency(movimiento.cargo)}
                      </div>
                      
                      {/* Saldo */}
                      <div className="w-24 text-right text-sm font-medium" style={{ color: colors.text }}>
                        {formatCurrency(movimiento.saldo)}
                      </div>
                      
                      {/* Número de movimiento */}
                      <div className="w-32 text-sm" style={{ color: colors.textSecondary }}>
                        {movimiento.numero_movimiento}
                      </div>
                    </div>

                    {/* Contenido expandido */}
                    {expandedMovimientos.has(movimiento.id) && (
                      <div 
                        className="px-4 py-6 border-b"
                        style={{ 
                          backgroundColor: colors.cardBackground,
                          borderColor: colors.border 
                        }}
                      >
                        <div className="max-w-4xl">
                          <h4 className="text-sm font-medium mb-3" style={{ color: colors.text }}>
                            📄 Contenido del movimiento #{movimiento.numero_movimiento}
                          </h4>
                          <div className="bg-gray-50 rounded-lg p-4 text-sm" style={{ 
                            backgroundColor: colors.secondary,
                            color: colors.textSecondary 
                          }}>
                            <p>Aquí se mostraría el contenido detallado del movimiento bancario.</p>
                            <p className="mt-2">
                              <strong>Banco:</strong> {movimiento.banco}<br/>
                              <strong>Fecha:</strong> {new Date(movimiento.fecha).toLocaleDateString('es-ES')}<br/>
                              <strong>Concepto:</strong> {movimiento.concepto}<br/>
                              <strong>Saldo:</strong> {formatCurrency(movimiento.saldo)}
                            </p>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ArchivoMovimientos;
