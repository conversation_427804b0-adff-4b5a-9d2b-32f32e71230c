# Instrucciones para eliminar chats de WhatsApp

La funcionalidad para eliminar chats de WhatsApp ya está implementada en la aplicación. Aquí está cómo usarla:

## En el backend (server.js)

El endpoint para eliminar chats ya existe en el archivo `server.js` (líneas 395-412):

```javascript
// Endpoint para eliminar un chat específico
app.delete('/delete-chat/:chatId', (req, res) => {
  const { chatId } = req.params;

  // Verificar si el chatId existe en el historial
  if (!chatHistory[chatId]) {
    return res.status(404).json({ error: 'Chat no encontrado' });
  }

  // Eliminar el chat del historial
  delete chatHistory[chatId];

  // Emitir evento para notificar al frontend
  io.emit('chat-deleted', { chatId });

  console.log(`Chat eliminado: ${chatId}`);
  res.json({ success: true, message: 'Chat eliminado correctamente' });
});
```

## En el frontend (CustomerService.tsx)

La funcionalidad para eliminar chats mediante clic derecho ya está implementada en el componente `CustomerService.tsx` (líneas 1870-1911):

```jsx
onContextMenu={(e) => {
  e.preventDefault();
  if (confirm(`¿Deseas eliminar el chat con ${chatInfo.name || phone}?`)) {
    // Formatear el ID del chat para el backend (agregar @c.us o @g.us)
    let formattedChatId = phone;
    if (phone.includes('(Grupo)')) {
      formattedChatId = phone.replace(' (Grupo)', '@g.us');
    } else {
      formattedChatId = `${phone}@c.us`;
    }

    // Llamar al endpoint para eliminar el chat
    api.delete(`/delete-chat/${formattedChatId}`)
      .then(response => {
        if (response.data.success) {
          // Eliminar el chat del estado local
          const updatedMessages = { ...messages };
          delete updatedMessages[phone];
          setMessages(updatedMessages);

          // Si el chat eliminado era el seleccionado, deseleccionarlo
          if (selectedChat === phone) {
            setSelectedChat(null);
          }

          // Eliminar los mensajes no leídos para este chat
          const updatedUnreadMessages = { ...unreadMessages };
          delete updatedUnreadMessages[phone];
          setUnreadMessages(updatedUnreadMessages);

          // Eliminar la última actividad para este chat
          const updatedLastActivity = { ...lastActivity };
          delete updatedLastActivity[phone];
          setLastActivity(updatedLastActivity);
        }
      })
      .catch(error => {
        console.error('Error al eliminar el chat:', error);
        alert('No se pudo eliminar el chat');
      });
  }
}}
```

También hay un indicador visual para informar a los usuarios sobre esta funcionalidad (líneas 1750-1753):

```jsx
<div className="flex justify-between items-center mb-2">
  <h3 className="font-semibold">Chats</h3>
  <span className="text-xs text-gray-500" title="Clic derecho para eliminar un chat">
    Clic derecho para eliminar
  </span>
</div>
```

## Cómo usar la funcionalidad

1. Haz clic derecho en cualquier chat de la lista
2. Se mostrará una confirmación preguntando si deseas eliminar el chat
3. Si confirmas, el chat se eliminará del servidor y desaparecerá de la lista

Esta funcionalidad ya está completamente implementada y no requiere cambios adicionales.
