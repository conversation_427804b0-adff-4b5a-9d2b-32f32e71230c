import React, { useState, useEffect, useRef } from 'react';
import { getBBVAMovements, getSantanderMovements, getTickets, createCoincidenceRecord } from '../utils/airtable';
import { useTheme } from '../contexts/ThemeContext';
import '../styles/animations.css';

interface BBVAMovement {
  Dia: string;
  'Concepto / Referencia': string;
  Abono: number;
  ID?: number; // Campo ID es numérico
}

interface SantanderMovement {
  Fecha: string;
  Concepto: string;
  Importe: number;
  ID?: number; // Campo ID es numérico
}

interface Ticket {
  url: string;
  imagen_base64?: string; // Nueva propiedad para la imagen en base64
  fileType?: string; // Tipo de archivo (image/jpeg, application/pdf, etc.)
}

const ITEMS_PER_PAGE = 10;

const PaymentRelations = () => {
  // Contexto de tema
  const { isDarkMode } = useTheme();

  // Estados para los datos
  const [bbvaMovements, setBbvaMovements] = useState<BBVAMovement[]>([]);
  const [santanderMovements, setSantanderMovements] = useState<SantanderMovement[]>([]);
  const [tickets, setTickets] = useState<Ticket[]>([]);

  // Estado para controlar qué banco se muestra
  const [activeBank, setActiveBank] = useState<'bbva' | 'santander'>('bbva');

  // Estados para la paginación
  const [currentBbvaPage, setCurrentBbvaPage] = useState(1);
  const [currentSantanderPage, setCurrentSantanderPage] = useState(1);
  const [currentTickPage, setCurrentTickPage] = useState(1);

  // Estados para la selección
  const [selectedBbvaMovement, setSelectedBbvaMovement] = useState<BBVAMovement | null>(null);
  const [selectedSantanderMovement, setSelectedSantanderMovement] = useState<SantanderMovement | null>(null);
  const [selectedTicket, setSelectedTicket] = useState<Ticket | null>(null);

  // Getter para el movimiento seleccionado actual (BBVA o Santander)
  const selectedMovement = activeBank === 'bbva' ? selectedBbvaMovement : selectedSantanderMovement;

  const [zoomedImage, setZoomedImage] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [notification, setNotification] = useState<{
    show: boolean;
    type: 'success' | 'error' | 'info';
    message: string;
    details?: string;
  }>({ show: false, type: 'info', message: '' });
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      // Obtener datos de BBVA
      const bbvaData = await getBBVAMovements();
      setBbvaMovements(bbvaData);

      // Obtener datos de Santander
      const santanderData = await getSantanderMovements();
      setSantanderMovements(santanderData);

      // Obtener tickets
      const ticketsData = await getTickets();
      setTickets(ticketsData);

      console.log(`Cargados ${bbvaData.length} movimientos BBVA y ${santanderData.length} movimientos Santander`);
    } catch (error) {
      console.error('Error al cargar datos:', error);
      setNotification({
        show: true,
        type: 'error',
        message: 'Error al cargar datos',
        details: 'No se pudieron cargar los movimientos o tickets. Por favor, recarga la página.'
      });
    }
  };

  // Función para convertir un archivo a base64
  const fileToBase64 = (file: File, fullUrl: boolean = false): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        const base64String = reader.result as string;
        if (fullUrl) {
          // Devolver la URL completa en formato base64 (con el prefijo "data:image/jpeg;base64,")
          resolve(base64String);
        } else {
          // Extraer solo la parte de datos (sin el prefijo)
          const base64Data = base64String.split(',')[1];
          resolve(base64Data);
        }
      };
      reader.onerror = error => reject(error);
    });
  };

  // Función para convertir una cadena base64 a una URL de imagen
  const base64ToImageUrl = (base64String: string, mimeType: string = 'image/jpeg'): string => {
    // Verificar si ya es una URL completa (comienza con data:)
    if (base64String && base64String.startsWith('data:')) {
      return base64String;
    }

    // Si es solo la cadena base64, añadir el prefijo adecuado
    if (base64String) {
      return `data:${mimeType};base64,${base64String}`;
    }

    // Si no hay datos, devolver una cadena vacía
    return '';
  };

  // Función para manejar la subida de imágenes y PDFs
  const handleImageUpload = async (file: File) => {
    if (!file) return;

    // Verificar que sea una imagen o un PDF
    if (!file.type.startsWith('image/') && file.type !== 'application/pdf') {
      alert('Por favor, selecciona un archivo de imagen (JPEG, PNG, etc.) o un PDF');
      return;
    }

    // Verificar el tamaño del archivo (máximo 10MB)
    const maxSizeInBytes = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSizeInBytes) {
      alert(`El archivo es demasiado grande (${(file.size / (1024 * 1024)).toFixed(2)} MB). El tamaño máximo permitido es 10 MB.`);
      return;
    }

    try {
      setIsUploading(true);

      // Convertir el archivo a base64 (URL completa)
      const base64Url = await fileToBase64(file, true);

      // Determinar el tipo de archivo
      const fileType = file.type.startsWith('image/') ? 'imagen' : 'PDF';

      // No subir el archivo a Airtable, solo mantenerlo en memoria
      console.log(`${fileType} cargado localmente: ${file.name} (${(file.size / 1024).toFixed(2)} KB)`);

      // Extraer la parte base64 sin el prefijo
      const base64Data = base64Url.split(',')[1];

      // Seleccionar automáticamente el ticket recién subido
      setSelectedTicket({
        url: base64Url,
        imagen_base64: base64Data, // Guardar también en imagen_base64
        fileType: file.type // Guardar el tipo de archivo para saber cómo mostrarlo
      });

    } catch (error) {
      console.error('Error al procesar el archivo:', error);
      alert('Error al procesar el archivo. Por favor, intenta de nuevo.');
    } finally {
      setIsUploading(false);
    }
  };

  // Función para manejar el clic en el botón de subir imagen
  const handleUploadButtonClick = () => {
    fileInputRef.current?.click();
  };

  // Función para manejar el cambio en el input de archivo
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleImageUpload(file);
    }
  };

  // Función para manejar el arrastrar y soltar
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    e.currentTarget.classList.add('border-blue-500');
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    e.currentTarget.classList.remove('border-blue-500');
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    e.currentTarget.classList.remove('border-blue-500');

    const file = e.dataTransfer.files[0];
    if (file) {
      handleImageUpload(file);
    }
  };

  const handleRelate = async () => {
    if (selectedMovement && selectedTicket) {
      console.log('Creando relación con los siguientes datos:');

      // Preparar datos según el banco seleccionado
      let dia, concepto, abono, idMovimiento, banco;

      if (activeBank === 'bbva' && selectedBbvaMovement) {
        dia = selectedBbvaMovement.Dia;
        concepto = selectedBbvaMovement['Concepto / Referencia'];
        abono = selectedBbvaMovement.Abono;
        idMovimiento = selectedBbvaMovement.ID;
        banco = 'bbva';

        console.log('- Día:', dia);
        console.log('- Concepto:', concepto);
        console.log('- Abono:', abono, 'Tipo:', typeof abono);
        console.log('- ID del movimiento:', idMovimiento !== undefined ? idMovimiento : 'No disponible');
        console.log('- Banco:', banco);
      } else if (activeBank === 'santander' && selectedSantanderMovement) {
        dia = selectedSantanderMovement.Fecha;
        concepto = selectedSantanderMovement.Concepto;
        abono = selectedSantanderMovement.Importe;
        idMovimiento = selectedSantanderMovement.ID;
        banco = 'santander';

        console.log('- Fecha:', dia);
        console.log('- Concepto:', concepto);
        console.log('- Importe:', abono, 'Tipo:', typeof abono);
        console.log('- ID del movimiento:', idMovimiento !== undefined ? idMovimiento : 'No disponible');
        console.log('- Banco:', banco);
      } else {
        console.error('No hay un movimiento seleccionado válido');
        return;
      }

      // Mostrar solo los primeros 100 caracteres de la URL para no saturar la consola
      const urlPreview = selectedTicket.url.length > 100
        ? selectedTicket.url.substring(0, 100) + '...'
        : selectedTicket.url;
      console.log('- URL del ticket (preview):', urlPreview);

      // Verificar si la URL es una cadena base64
      const isBase64Url = selectedTicket.url.startsWith('data:');
      console.log('- ¿Es URL base64?:', isBase64Url);

      // Mostrar notificación de procesamiento
      setNotification({
        show: true,
        type: 'info',
        message: 'Procesando relación...',
        details: 'Enviando datos al servidor'
      });

      try {
        // Enviar datos al webhook
        const success = await createCoincidenceRecord({
          dia: dia,
          concepto: concepto,
          abono: String(abono), // Convertir explícitamente a string
          url: selectedTicket.url,
          id_movimiento: idMovimiento !== undefined ? String(idMovimiento) : '', // Convertir ID numérico a string
          banco: banco // Incluir el banco
        });

        if (success) {
          // Mostrar notificación de éxito
          setNotification({
            show: true,
            type: 'success',
            message: 'Relación creada exitosamente',
            details: `Movimiento del ${banco === 'bbva' ? 'día' : 'fecha'} ${dia} por $${abono} relacionado correctamente`
          });

          // Limpiar selecciones y actualizar datos
          if (activeBank === 'bbva') {
            setSelectedBbvaMovement(null);
          } else {
            setSelectedSantanderMovement(null);
          }
          setSelectedTicket(null);
          await fetchData();

          // Ocultar la notificación después de 5 segundos
          setTimeout(() => {
            setNotification(prev => ({ ...prev, show: false }));
          }, 5000);
        } else {
          // Mostrar notificación de error
          setNotification({
            show: true,
            type: 'error',
            message: 'Error al crear la relación',
            details: 'Por favor, revisa la consola para más detalles e intenta de nuevo'
          });

          // Ocultar la notificación después de 5 segundos
          setTimeout(() => {
            setNotification(prev => ({ ...prev, show: false }));
          }, 5000);
        }
      } catch (error) {
        console.error('Error al crear la relación:', error);

        // Mostrar notificación de error
        setNotification({
          show: true,
          type: 'error',
          message: 'Error al crear la relación',
          details: error instanceof Error ? error.message : 'Error desconocido'
        });

        // Ocultar la notificación después de 5 segundos
        setTimeout(() => {
          setNotification(prev => ({ ...prev, show: false }));
        }, 5000);
      }
    }
  };

  // Calcular páginas para BBVA
  const totalBbvaPages = Math.ceil(bbvaMovements.length / ITEMS_PER_PAGE);
  const paginatedBbvaMovements = bbvaMovements.slice(
    (currentBbvaPage - 1) * ITEMS_PER_PAGE,
    currentBbvaPage * ITEMS_PER_PAGE
  );

  // Calcular páginas para Santander
  const totalSantanderPages = Math.ceil(santanderMovements.length / ITEMS_PER_PAGE);
  const paginatedSantanderMovements = santanderMovements.slice(
    (currentSantanderPage - 1) * ITEMS_PER_PAGE,
    currentSantanderPage * ITEMS_PER_PAGE
  );

  // Calcular páginas para tickets
  const totalTickPages = Math.ceil(tickets.length / ITEMS_PER_PAGE);
  const paginatedTickets = tickets.slice(
    (currentTickPage - 1) * ITEMS_PER_PAGE,
    currentTickPage * ITEMS_PER_PAGE
  );

  // Obtener el total de páginas según el banco activo
  const totalMovPages = activeBank === 'bbva' ? totalBbvaPages : totalSantanderPages;

  return (
    <div className={`space-y-6 border-2 rounded-lg p-4 m-2 transition-colors duration-300 ${
      isDarkMode
        ? 'border-gray-600 bg-gray-900'
        : 'border-gray-300 bg-white'
    }`}>

      {/* Crear Relación */}
      <div className={`p-1 rounded transition-colors duration-300 ${
        isDarkMode ? 'bg-gray-800' : 'bg-white'
      }`}>
        <h2 className={`text-sm font-medium mb-1 transition-colors duration-300 ${
          isDarkMode ? 'text-gray-200' : 'text-gray-900'
        }`}>Crear Relación</h2>
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-1">
          <div>
            <h3 className={`text-xs font-medium mb-1 transition-colors duration-300 ${
              isDarkMode ? 'text-gray-300' : 'text-gray-900'
            }`}>Movimiento Seleccionado:</h3>
            {selectedMovement ? (
              <div className={`p-2 rounded transition-colors duration-300 ${
                isDarkMode ? 'bg-gray-700' : 'bg-gray-50'
              }`}>
                {activeBank === 'bbva' && selectedBbvaMovement ? (
                  <>
                    <p className={`transition-colors duration-300 ${
                      isDarkMode ? 'text-gray-200' : 'text-gray-900'
                    }`}>Día: {selectedBbvaMovement.Dia}</p>
                    <p className={`transition-colors duration-300 ${
                      isDarkMode ? 'text-gray-200' : 'text-gray-900'
                    }`}>Concepto: {selectedBbvaMovement['Concepto / Referencia']}</p>
                    <p className={`transition-colors duration-300 ${
                      isDarkMode ? 'text-gray-200' : 'text-gray-900'
                    }`}>Abono: {selectedBbvaMovement.Abono}</p>
                    <p className={`text-xs transition-colors duration-300 ${
                      isDarkMode ? 'text-gray-400' : 'text-gray-500'
                    }`}>Banco: BBVA</p>
                    <button
                      onClick={() => setSelectedBbvaMovement(null)}
                      className="mt-2 text-sm text-red-500 hover:underline"
                    >
                      ❌ Quitar selección
                    </button>
                  </>
                ) : activeBank === 'santander' && selectedSantanderMovement ? (
                  <>
                    <p className={`transition-colors duration-300 ${
                      isDarkMode ? 'text-gray-200' : 'text-gray-900'
                    }`}>Fecha: {selectedSantanderMovement.Fecha}</p>
                    <p className={`transition-colors duration-300 ${
                      isDarkMode ? 'text-gray-200' : 'text-gray-900'
                    }`}>Concepto: {selectedSantanderMovement.Concepto}</p>
                    <p className={`transition-colors duration-300 ${
                      isDarkMode ? 'text-gray-200' : 'text-gray-900'
                    }`}>Importe: {selectedSantanderMovement.Importe}</p>
                    <p className={`text-xs transition-colors duration-300 ${
                      isDarkMode ? 'text-gray-400' : 'text-gray-500'
                    }`}>Banco: Santander</p>
                    <button
                      onClick={() => setSelectedSantanderMovement(null)}
                      className="mt-2 text-sm text-red-500 hover:underline"
                    >
                      ❌ Quitar selección
                    </button>
                  </>
                ) : null}
              </div>
            ) : (
              <p className={`transition-colors duration-300 ${
                isDarkMode ? 'text-gray-400' : 'text-gray-500'
              }`}>Ningún movimiento seleccionado</p>
            )}
          </div>
          <div>
            <h3 className={`text-xs font-medium mb-1 transition-colors duration-300 ${
              isDarkMode ? 'text-gray-300' : 'text-gray-900'
            }`}>Ticket Seleccionado:</h3>
            {selectedTicket ? (
              <div className={`p-2 rounded transition-colors duration-300 ${
                isDarkMode ? 'bg-gray-700' : 'bg-gray-50'
              }`}>
                {selectedTicket.fileType && selectedTicket.fileType === 'application/pdf' ? (
                  <div className="mb-2">
                    <div className="flex items-center justify-center w-32 h-32 bg-gray-100 border border-gray-300 rounded mb-2">
                      <div className="text-center">
                        <svg className="mx-auto h-10 w-10 text-red-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                        </svg>
                        <p className="text-xs mt-1">Documento PDF</p>
                      </div>
                    </div>
                    <a
                      href={selectedTicket.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-sm text-blue-500 hover:underline block text-center mb-2"
                    >
                      Ver PDF
                    </a>
                  </div>
                ) : selectedTicket.imagen_base64 ? (
                  // Mostrar imagen desde imagen_base64
                  <img
                    src={base64ToImageUrl(selectedTicket.imagen_base64)}
                    alt="Selected Ticket (Base64)"
                    className="w-full max-w-[128px] h-auto aspect-square object-cover mb-2 mx-auto"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.onerror = null;
                      target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGQ9Ik0yNCAxMmMwIDYuNjIzLTUuMzc3IDEyLTEyIDEycy0xMi01LjM3Ny0xMi0xMiA1LjM3Ny0xMiAxMi0xMiAxMiA1LjM3NyAxMiAxMnptLTEgMGMwIDYuMDcxLTQuOTI5IDExLTExIDExcy0xMS00LjkyOS0xMS0xMSA0LjkyOS0xMSAxMS0xMSAxMSA0LjkyOSAxMSAxMXptLTExLjUgNC4wMDFoMXYtOC4wMDJoLTF2OC4wMDJ6bTAgMy4wMDFoMXYtMi4wMDFoLTF2Mi4wMDF6Ii8+PC9zdmc+';
                    }}
                  />
                ) : (
                  // Si no hay imagen_base64, mostrar un mensaje
                  <div className="flex items-center justify-center w-32 h-32 bg-gray-100 border border-gray-300 rounded mb-2">
                    <div className="text-center">
                      <svg className="mx-auto h-8 w-8 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      <p className="text-xs mt-1 text-gray-500">Sin imagen</p>
                    </div>
                  </div>
                )}
                <button
                  onClick={() => setSelectedTicket(null)}
                  className="text-sm text-red-500 hover:underline"
                >
                  ❌ Quitar selección
                </button>
              </div>
            ) : (
              <div>
                {/* Área para arrastrar y soltar */}
                <div
                  className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center cursor-pointer hover:border-blue-500 transition-colors"
                  onClick={handleUploadButtonClick}
                  onDragOver={handleDragOver}
                  onDragLeave={handleDragLeave}
                  onDrop={handleDrop}
                >
                  {isUploading ? (
                    <div className="text-blue-500">
                      <svg className="animate-spin h-5 w-5 mx-auto mb-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      <p className="text-sm">Subiendo archivo...</p>
                    </div>
                  ) : (
                    <>
                      <svg className="mx-auto h-8 w-8 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                      </svg>
                      <p className="mt-1 text-sm text-gray-500">Haz clic o arrastra un archivo aquí</p>
                      <p className="mt-1 text-xs text-gray-400">PNG, JPG, GIF, PDF hasta 10MB</p>
                    </>
                  )}
                </div>

                {/* Input de archivo oculto */}
                <input
                  type="file"
                  ref={fileInputRef}
                  className="hidden"
                  accept="image/*,application/pdf"
                  onChange={handleFileInputChange}
                />
              </div>
            )}
          </div>
        </div>
        <div className="flex justify-center mt-1">
          <button
            onClick={handleRelate}
            disabled={!selectedMovement || !selectedTicket}
            className={`px-2 py-0.5 rounded font-medium text-xs transition-colors ${
              !selectedMovement || !selectedTicket
                ? 'bg-purple-200 text-gray-600'
                : 'bg-green-500 text-white hover:bg-green-600'
            }`}
          >
            {!selectedMovement && !selectedTicket ? (
              'Selecciona un movimiento y un ticket'
            ) : !selectedMovement ? (
              'Selecciona un movimiento'
            ) : !selectedTicket ? (
              'Selecciona un ticket'
            ) : (
              'Crear Relación'
            )}
          </button>
        </div>
      </div>

      {/* Grid de tablas */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Movimientos Bancarios */}
        <div className={`p-4 rounded-lg transition-colors duration-300 ${
          isDarkMode ? 'bg-gray-800' : 'bg-white'
        }`}>
          {/* Selector de banco */}
          <div className={`flex flex-wrap mb-4 border-b transition-colors duration-300 ${
            isDarkMode ? 'border-gray-600' : 'border-gray-200'
          }`}>
            <button
              onClick={() => setActiveBank('bbva')}
              className={`px-3 sm:px-4 py-2 font-medium text-sm sm:text-base flex-1 transition-colors duration-300 ${activeBank === 'bbva'
                ? isDarkMode
                  ? 'text-blue-400 border-b-2 border-blue-400 bg-gray-700'
                  : 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'
                : isDarkMode
                  ? 'text-gray-400 hover:text-gray-200'
                  : 'text-gray-500 hover:text-gray-700'}`}
            >
              BBVA
            </button>
            <button
              onClick={() => setActiveBank('santander')}
              className={`px-3 sm:px-4 py-2 font-medium text-sm sm:text-base flex-1 transition-colors duration-300 ${activeBank === 'santander'
                ? isDarkMode
                  ? 'text-blue-400 border-b-2 border-blue-400 bg-gray-700'
                  : 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'
                : isDarkMode
                  ? 'text-gray-400 hover:text-gray-200'
                  : 'text-gray-500 hover:text-gray-700'}`}
            >
              Santander
            </button>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full">
              <thead>
                <tr className={`m-0.5 transition-colors duration-300 ${
                  isDarkMode ? 'bg-gray-700/50' : 'bg-gray-50/50'
                }`}>
                  {activeBank === 'bbva' ? (
                    <>
                      <th className={`px-2 py-2 w-20 text-center transition-colors duration-300 ${
                        isDarkMode ? 'text-gray-200' : 'text-gray-900'
                      }`}>Día</th>
                      <th className={`px-4 py-2 text-center transition-colors duration-300 ${
                        isDarkMode ? 'text-gray-200' : 'text-gray-900'
                      }`}>Concepto/Referencia</th>
                      <th className={`px-2 py-2 w-24 text-center transition-colors duration-300 ${
                        isDarkMode ? 'text-gray-200' : 'text-gray-900'
                      }`}>Abono</th>
                    </>
                  ) : (
                    <>
                      <th className={`px-4 py-2 text-center transition-colors duration-300 ${
                        isDarkMode ? 'text-gray-200' : 'text-gray-900'
                      }`}>Fecha</th>
                      <th className={`px-4 py-2 text-center transition-colors duration-300 ${
                        isDarkMode ? 'text-gray-200' : 'text-gray-900'
                      }`}>Concepto</th>
                      <th className={`px-4 py-2 text-center transition-colors duration-300 ${
                        isDarkMode ? 'text-gray-200' : 'text-gray-900'
                      }`}>Importe</th>
                    </>
                  )}
                  <th className={`px-2 py-2 w-20 text-center transition-colors duration-300 ${
                    isDarkMode ? 'text-gray-200' : 'text-gray-900'
                  }`}>Acción</th>
                </tr>
              </thead>
              <tbody>
                {activeBank === 'bbva' ? (
                  // Movimientos BBVA
                  paginatedBbvaMovements.map((movement, index) => (
                    <tr key={index} className={`transition-colors duration-300 ${
                      selectedBbvaMovement === movement
                        ? isDarkMode ? 'bg-gray-600' : 'bg-blue-50'
                        : isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-50'
                    }`}>
                      <td className={`px-2 py-2 w-20 text-center transition-colors duration-300 ${
                        isDarkMode ? 'text-gray-200' : 'text-gray-900'
                      }`}>{movement.Dia}</td>
                      <td className={`px-4 py-2 text-center transition-colors duration-300 ${
                        isDarkMode ? 'text-gray-200' : 'text-gray-900'
                      }`} title={movement['Concepto / Referencia']}>
                        {movement['Concepto / Referencia']}
                      </td>
                      <td className={`px-2 py-2 w-24 text-center transition-colors duration-300 ${
                        isDarkMode ? 'text-gray-200' : 'text-gray-900'
                      }`}>{movement.Abono}</td>
                      <td className="px-2 py-2 w-20 text-center">
                        <button
                          onClick={() => setSelectedBbvaMovement(movement)}
                          className="px-3 py-1 bg-blue-300 text-gray-700 rounded hover:bg-blue-400 transition-colors duration-300"
                        >
                          Seleccionar
                        </button>
                      </td>
                    </tr>
                  ))
                ) : (
                  // Movimientos Santander
                  paginatedSantanderMovements.map((movement, index) => (
                    <tr key={index} className={`transition-colors duration-300 ${
                      selectedSantanderMovement === movement
                        ? isDarkMode ? 'bg-gray-600' : 'bg-blue-50'
                        : isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-50'
                    }`}>
                      <td className={`px-4 py-2 text-center transition-colors duration-300 ${
                        isDarkMode ? 'text-gray-200' : 'text-gray-900'
                      }`}>{movement.Fecha}</td>
                      <td className={`px-4 py-2 text-center transition-colors duration-300 ${
                        isDarkMode ? 'text-gray-200' : 'text-gray-900'
                      }`} title={movement.Concepto}>
                        {movement.Concepto}
                      </td>
                      <td className={`px-4 py-2 text-center transition-colors duration-300 ${
                        isDarkMode ? 'text-gray-200' : 'text-gray-900'
                      }`}>{movement.Importe}</td>
                      <td className="px-4 py-2 text-center">
                        <button
                          onClick={() => setSelectedSantanderMovement(movement)}
                          className="px-3 py-1 bg-blue-300 text-gray-700 rounded hover:bg-blue-400 transition-colors duration-300"
                        >
                          Seleccionar
                        </button>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
          <div className="flex flex-wrap justify-between items-center mt-4 gap-2">
            <button
              disabled={activeBank === 'bbva' ? currentBbvaPage === 1 : currentSantanderPage === 1}
              onClick={() => {
                if (activeBank === 'bbva') {
                  setCurrentBbvaPage(p => p - 1);
                } else {
                  setCurrentSantanderPage(p => p - 1);
                }
              }}
              className="px-4 py-2 bg-purple-300 text-gray-700 rounded disabled:opacity-50 hover:bg-purple-400 text-sm sm:text-base min-w-[90px] text-center transition-colors"
            >
              ← Anterior
            </button>
            <span className="text-sm sm:text-base text-gray-600 text-center flex-grow">
              Página {activeBank === 'bbva' ? currentBbvaPage : currentSantanderPage} de {totalMovPages}
            </span>
            <button
              disabled={activeBank === 'bbva'
                ? currentBbvaPage >= totalBbvaPages
                : currentSantanderPage >= totalSantanderPages}
              onClick={() => {
                if (activeBank === 'bbva') {
                  setCurrentBbvaPage(p => p + 1);
                } else {
                  setCurrentSantanderPage(p => p + 1);
                }
              }}
              className="px-4 py-2 bg-purple-300 text-gray-700 rounded disabled:opacity-50 hover:bg-purple-400 text-sm sm:text-base min-w-[90px] text-center transition-colors"
            >
              Siguiente →
            </button>
          </div>
        </div>

        {/* Tickets */}
        <div className={`p-4 rounded-lg transition-colors duration-300 ${
          isDarkMode ? 'bg-gray-800' : 'bg-white'
        }`}>
          <h2 className={`text-lg font-semibold mb-4 transition-colors duration-300 ${
            isDarkMode ? 'text-gray-200' : 'text-gray-900'
          }`}>Tickets Recibidos</h2>
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2">
            {paginatedTickets.map((ticket, index) => (
              <div
                key={index}
                className={`p-2 border rounded ${selectedTicket === ticket ? 'border-blue-500' : 'border-gray-200'}`}
              >
                {ticket.fileType && ticket.fileType === 'application/pdf' ? (
                  <div
                    className="w-full h-40 flex items-center justify-center bg-gray-100 border border-gray-300 rounded mb-2 cursor-pointer"
                    onClick={() => window.open(ticket.url, '_blank')}
                  >
                    <div className="text-center">
                      <svg className="mx-auto h-16 w-16 text-red-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                      </svg>
                      <p className="text-sm mt-2">Documento PDF</p>
                      <p className="text-xs text-blue-500 mt-1">Click para ver</p>
                    </div>
                  </div>
                ) : ticket.imagen_base64 ? (
                  // Mostrar imagen desde imagen_base64
                  <img
                    src={base64ToImageUrl(ticket.imagen_base64)}
                    alt="Ticket (Base64)"
                    className="w-full h-40 object-cover mb-2 cursor-pointer rounded"
                    onClick={() => setZoomedImage(base64ToImageUrl(ticket.imagen_base64))}
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.onerror = null;
                      target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGQ9Ik0yNCAxMmMwIDYuNjIzLTUuMzc3IDEyLTEyIDEycy0xMi01LjM3Ny0xMi0xMiA1LjM3Ny0xMiAxMi0xMiAxMiA1LjM3NyAxMiAxMnptLTEgMGMwIDYuMDcxLTQuOTI5IDExLTExIDExcy0xMS00LjkyOS0xMS0xMSA0LjkyOS0xMSAxMS0xMSAxMSA0LjkyOSAxMSAxMXptLTExLjUgNC4wMDFoMXYtOC4wMDJoLTF2OC4wMDJ6bTAgMy4wMDFoMXYtMi4wMDFoLTF2Mi4wMDF6Ii8+PC9zdmc+';
                    }}
                  />
                ) : (
                  // Si no hay imagen_base64, mostrar un mensaje
                  <div className="w-full h-40 flex items-center justify-center bg-gray-100 border border-gray-300 rounded mb-2">
                    <div className="text-center">
                      <svg className="mx-auto h-10 w-10 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      <p className="text-sm mt-2 text-gray-500">Sin imagen disponible</p>
                    </div>
                  </div>
                )}
                <button
                  onClick={() => setSelectedTicket(ticket)}
                  className="w-full px-3 py-1 bg-blue-300 text-gray-700 rounded hover:bg-blue-400"
                >
                  Seleccionar
                </button>
              </div>
            ))}
          </div>
          <div className="flex flex-wrap justify-between items-center mt-4 gap-2">
            <button
              disabled={currentTickPage === 1}
              onClick={() => setCurrentTickPage((p) => p - 1)}
              className="px-4 py-2 bg-purple-300 text-gray-700 rounded disabled:opacity-50 hover:bg-purple-400 text-sm sm:text-base min-w-[90px] text-center transition-colors"
            >
              ← Anterior
            </button>
            <span className="text-sm sm:text-base text-gray-600 text-center flex-grow">
              Página {currentTickPage} de {totalTickPages}
            </span>
            <button
              disabled={currentTickPage >= totalTickPages}
              onClick={() => setCurrentTickPage((p) => p + 1)}
              className="px-4 py-2 bg-purple-300 text-gray-700 rounded disabled:opacity-50 hover:bg-purple-400 text-sm sm:text-base min-w-[90px] text-center transition-colors"
            >
              Siguiente →
            </button>
          </div>
        </div>
      </div>

      {/* Modal de imagen ampliada */}
      {zoomedImage && (
        <div
          className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50"
          onClick={() => setZoomedImage(null)}
        >
          {zoomedImage.startsWith('data:application/pdf') ? (
            <div className="bg-white p-4 rounded-lg shadow-lg max-w-[90%] max-h-[90%] w-4/5 h-4/5" onClick={(e) => e.stopPropagation()}>
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-lg font-medium">Documento PDF</h3>
                <button
                  onClick={() => setZoomedImage(null)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  ✕
                </button>
              </div>
              <iframe
                src={zoomedImage}
                className="w-full h-[calc(100%-40px)]"
                title="PDF Viewer"
              />
            </div>
          ) : (
            <img
              src={zoomedImage}
              alt="Zoom"
              className="max-w-[90%] max-h-[90%] rounded shadow-lg"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.onerror = null;
                target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGQ9Ik0yNCAxMmMwIDYuNjIzLTUuMzc3IDEyLTEyIDEycy0xMi01LjM3Ny0xMi0xMiA1LjM3Ny0xMiAxMi0xMiAxMiA1LjM3NyAxMiAxMnptLTEgMGMwIDYuMDcxLTQuOTI5IDExLTExIDExcy0xMS00LjkyOS0xMS0xMSA0LjkyOS0xMSAxMS0xMSAxMSA0LjkyOSAxMSAxMXptLTExLjUgNC4wMDFoMXYtOC4wMDJoLTF2OC4wMDJ6bTAgMy4wMDFoMXYtMi4wMDFoLTF2Mi4wMDF6Ii8+PC9zdmc+';
              }}
            />
          )}
        </div>
      )}

      {/* Notificación */}
      {notification.show && (
        <div
          className={`fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 flex items-start space-x-4 max-w-md animate-fade-in ${
            notification.type === 'success' ? 'bg-green-500 text-white' :
            notification.type === 'error' ? 'bg-red-500 text-white' :
            'bg-blue-500 text-white'
          }`}
        >
          <div className="flex-shrink-0 pt-0.5">
            {notification.type === 'success' && (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            )}
            {notification.type === 'error' && (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            )}
            {notification.type === 'info' && (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            )}
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-medium">{notification.message}</h3>
            {notification.details && (
              <p className="mt-1 text-sm opacity-90">{notification.details}</p>
            )}
          </div>
          <button
            onClick={() => setNotification(prev => ({ ...prev, show: false }))}
            className="flex-shrink-0 text-white hover:text-gray-200 transition-colors"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
      )}
    </div>
  );
};

export default PaymentRelations;
