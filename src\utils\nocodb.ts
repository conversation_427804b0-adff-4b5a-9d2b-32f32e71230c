import axios, { AxiosRequestConfig } from 'axios';

// Configuración centralizada para la API de NocoDB
const NOCODB_API_URL = 'https://aanocodb.wifigo.com.mx/api/v2/tables';
const NOCODB_API_KEY = 'S5q2DMhzneGng7KXEK7C1wlklvWrjvsCbEovxPdb';

// Creación de una instancia de axios con la configuración base
const apiClient = axios.create({
  baseURL: NOCODB_API_URL,
  headers: {
    'Accept': 'application/json',
    'Content-Type': 'application/json',
    'xc-token': NOCODB_API_KEY,
  },
  timeout: 60000, // 60 segundos de timeout
});

/**
 * Función genérica para realizar peticiones a la API de NocoDB.
 * @param config La configuración para la petición de axios.
 * @returns La data de la respuesta de la API.
 */
const nocoRequest = async (config: AxiosRequestConfig) => {
  try {
    const response = await apiClient.request(config);
    return response.data;
  } catch (error: any) {
    console.error(`Error en la petición a NocoDB (${config.url}):`, error.response?.data || error.message);
    throw error;
  }
};

/**
 * Obtiene registros de una tabla.
 * @param tableName El ID o nombre de la tabla.
 * @param params Los parámetros de la consulta (limit, where, etc.).
 * @returns Una promesa que resuelve con los datos de la tabla.
 */
export const getRecords = (tableName: string, params: any = {}) => {
  return nocoRequest({
    method: 'get',
    url: `/${tableName}/records`,
    params,
  });
};

/**
 * Crea un nuevo registro en una tabla.
 * @param tableName El ID o nombre de la tabla.
 * @param data El objeto con los datos del nuevo registro.
 * @returns Una promesa que resuelve con el nuevo registro creado.
 */
export const createRecord = (tableName: string, data: any) => {
  return nocoRequest({
    method: 'post',
    url: `/${tableName}/records`,
    data,
  });
};

/**
 * Actualiza un registro existente en una tabla.
 * @param tableName El ID o nombre de la tabla.
 * @param record El objeto que se va a actualizar. Debe contener el campo 'Id'.
 * @returns Una promesa que resuelve con el registro actualizado.
 */
export const updateRecord = (tableName: string, record: any) => {
  return nocoRequest({
    method: 'patch',
    url: `/${tableName}/records`,
    data: record,
  });
};

/**
 * Elimina un registro de una tabla.
 * @param tableName El ID o nombre de la tabla.
 * @param recordId El ID del registro a eliminar.
 * @returns Una promesa que resuelve con el resultado de la operación.
 */
export const deleteRecord = (tableName: string, recordId: number) => {
  return nocoRequest({
    method: 'delete',
    url: `/${tableName}/records`,
    data: { Id: recordId },
  });
};
