# Configuración de Evolution API para WifiGo

Este documento explica cómo configurar Evolution API para conectarse directamente a instancias existentes de WhatsApp sin necesidad de escanear códigos QR cada vez.

## Requisitos previos

1. Tener Evolution API instalado y funcionando
2. Conocer la URL y la clave API de tu instancia de Evolution API
3. Tener instancias de WhatsApp ya configuradas en Evolution API

## Pasos para la configuración

### 1. Editar el archivo de configuración

Abre el archivo `src/backend/evolution-api-config.js` y actualiza los siguientes valores:

```javascript
// Configuración para la conexión con Evolution API
const EVOLUTION_API_CONFIG = {
  // URL base de la API de Evolution
  baseUrl: 'http://tu-servidor:8080', // Cambia esto a la URL de tu instancia de Evolution API
  
  // Clave de API para autenticación
  apiKey: 'tu-api-key-aqui', // Cambia esto a tu clave de API de Evolution
  
  // Instancias de WhatsApp configuradas en Evolution API
  instances: {
    client1: 'instance1', // Nombre de la instancia en Evolution API
    client2: 'instance2'  // Nombre de la instancia en Evolution API
  },
  
  // Tiempo de espera para las solicitudes en milisegundos
  timeout: 30000
};
```

Reemplaza:
- `http://tu-servidor:8080` con la URL de tu instancia de Evolution API
- `tu-api-key-aqui` con la clave API de tu instancia de Evolution API
- `instance1` y `instance2` con los nombres de tus instancias de WhatsApp en Evolution API

### 2. Verificar que las instancias existen en Evolution API

Asegúrate de que las instancias que has configurado en el archivo `evolution-api-config.js` existen en tu servidor de Evolution API. Puedes verificarlo accediendo a:

```
http://tu-servidor:8080/manager
```

### 3. Iniciar el servidor

Una vez configurado, inicia el servidor:

```
npm run dev
```

### 4. Conectar a las instancias

1. En la interfaz de usuario, haz clic en "WhatsApp" en el menú lateral
2. Selecciona "Conectar"
3. Verás tus instancias de Evolution API marcadas con una etiqueta "Evolution API"
4. Haz clic en "Conectar" para cada instancia

Si las instancias ya están autenticadas en Evolution API, se conectarán automáticamente sin necesidad de escanear un código QR.

## Solución de problemas

### La instancia no aparece como conectada

1. Verifica que la instancia esté correctamente configurada en Evolution API
2. Comprueba que el nombre de la instancia en `evolution-api-config.js` coincide exactamente con el nombre en Evolution API
3. Asegúrate de que la URL y la clave API son correctas

### Error al conectar

Si recibes un error al intentar conectar, verifica:

1. Que Evolution API esté funcionando correctamente
2. Que la instancia exista y esté en estado válido
3. Que la clave API tenga permisos suficientes

## Notas adicionales

- Las instancias de Evolution API deben estar previamente autenticadas para poder conectarse sin escanear un código QR
- Si una instancia no está autenticada, se mostrará un código QR que deberás escanear con WhatsApp
- Una vez autenticada, la instancia permanecerá conectada incluso después de reiniciar el servidor
