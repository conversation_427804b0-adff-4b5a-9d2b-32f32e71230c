import React from 'react';
import { BarChart } from 'lucide-react';

const Dashboard: React.FC = () => {
  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Dashboard</h1>
      
      <div className="grid grid-cols-3 gap-6">
        {/* Summary Cards */}
        <div className="bg-white p-6 rounded-lg shadow-lg">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Total Clientes</h3>
            <BarChart className="w-6 h-6 text-blue-500" />
          </div>
          <p className="text-3xl font-bold">1,234</p>
          <p className="text-sm text-gray-500 mt-2">+12% desde el mes pasado</p>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-lg">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Pagos Pendientes</h3>
            <BarChart className="w-6 h-6 text-yellow-500" />
          </div>
          <p className="text-3xl font-bold">45</p>
          <p className="text-sm text-gray-500 mt-2">Últimas 24 horas</p>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-lg">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Tickets Abiertos</h3>
            <BarChart className="w-6 h-6 text-green-500" />
          </div>
          <p className="text-3xl font-bold">28</p>
          <p className="text-sm text-gray-500 mt-2">5 requieren atención</p>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white p-6 rounded-lg shadow-lg">
        <h2 className="text-xl font-semibold mb-4">Actividad Reciente</h2>
        <div className="space-y-4">
          {[1, 2, 3, 4, 5].map((item) => (
            <div key={item} className="flex items-center justify-between py-3 border-b last:border-0">
              <div>
                <p className="font-medium">Pago Recibido - Cliente #{item}</p>
                <p className="text-sm text-gray-500">Hace {item} horas</p>
              </div>
              <span className="text-green-500 font-medium">$1,{item}99.00</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;