# Configuración para Producción

## 🚀 Variables de Entorno Requeridas

Para desplegar el backend en producción (`ccbak.wifigo.com.mx`), configurar las siguientes variables de entorno en el servidor:

### Variables Principales:
```bash
NODE_ENV=production
FRONTEND_URL=https://central.wifigo.com.mx
WEBHOOK_URL=https://aawebhook.wifigo.com.mx/webhook/enviar-mensaje
PORT=3002
```

## 📋 Instrucciones de Despliegue

### 1. En el servidor de producción:
```bash
# Configurar variables de entorno
export NODE_ENV=production
export FRONTEND_URL=https://central.wifigo.com.mx
export WEBHOOK_URL=https://aawebhook.wifigo.com.mx/webhook/enviar-mensaje
export PORT=3002
```

### 2. O crear archivo .env.production:
```bash
# Copiar el archivo .env.production al servidor
# El archivo ya está configurado con las URLs correctas
```

### 3. Iniciar el servidor:
```bash
# Instalar dependencias
npm install

# Iniciar en modo producción
npm start
# o
NODE_ENV=production node index.js
```

## 🌐 URLs de Producción

- **Frontend**: https://central.wifigo.com.mx
- **Backend**: https://ccbak.wifigo.com.mx
- **Webhook**: https://aawebhook.wifigo.com.mx/webhook/enviar-mensaje

## ✅ Verificación

Una vez desplegado, verificar:

1. **Página principal**: https://ccbak.wifigo.com.mx
   - Debe mostrar "✅ API Funcionando Correctamente"

2. **Test de conexión**: https://ccbak.wifigo.com.mx/test-socket
   - Debe retornar JSON con información de clientes conectados

3. **CORS**: El frontend debe poder conectarse sin errores de CORS

## 🔧 Configuración del Servidor Web

Si usas Nginx, configurar proxy reverso:

```nginx
server {
    listen 80;
    server_name ccbak.wifigo.com.mx;
    
    location / {
        proxy_pass http://localhost:3002;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## 📝 Notas Importantes

- El servidor está configurado para cargar automáticamente `.env.production` cuando `NODE_ENV=production`
- Las URLs ya están configuradas para los dominios de producción
- El puerto 3002 debe estar disponible en el servidor
- Asegurar que el firewall permita conexiones en el puerto 3002
